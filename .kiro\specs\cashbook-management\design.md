# Design Document

## Overview

The Cashbook Management system is a comprehensive financial tracking application built as an integrated module within the existing Next.js App. The system provides multi-user cashbook management with real-time collaboration, transaction tracking, and financial overview capabilities. The architecture leverages Neon PostgreSQL for data persistence with Row-Level Security (RLS) for secure multi-tenant access control.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js App]
        B[Apps Screen]
        C[Cashbook Components]
    end
    
    subgraph "Navigation Layer"
        D[Main Navigator]
        E[Apps Navigator]
        F[Cashbook Navigator]
    end
    
    subgraph "State Management"
        G[Auth Context]
        H[Cashbook Context]
        I[Theme Context]
    end
    
    subgraph "API Layer"
        J[Neon MCP Server]
        K[Database Connection]
    end
    
    subgraph "Database Layer"
        L[(Neon PostgreSQL)]
        M[RLS Policies]
        N[Database Schema]
    end
    
    A --> D
    D --> E
    E --> F
    F --> C
    C --> H
    H --> G
    C --> J
    J --> K
    K --> L
    L --> M
    L --> N
```

### Database Architecture

The system uses a multi-tenant architecture with Row-Level Security (RLS) to ensure data isolation and proper access control:

```mermaid
erDiagram
    CASHBOOKS {
        uuid id PK
        text name
        text description
        uuid owner_id FK
        text currency
        timestamp created_at
        timestamp updated_at
    }
    
    TRANSACTIONS {
        uuid id PK
        uuid cashbook_id FK
        decimal amount
        text type
        uuid category_id FK
        text description
        date date
        uuid created_by FK
        timestamp created_at
    }
    
    CATEGORIES {
        uuid id PK
        text name
        text type
        boolean is_default
        uuid created_by FK
        timestamp created_at
    }
    
    CASHBOOK_COLLABORATORS {
        uuid id PK
        uuid cashbook_id FK
        uuid user_id FK
        text role
        timestamp created_at
        timestamp updated_at
    }
    
    USERS {
        uuid id PK
        text email
        text name
        timestamp created_at
    }
    
    CASHBOOKS ||--o{ TRANSACTIONS : contains
    CASHBOOKS ||--o{ CASHBOOK_COLLABORATORS : has
    CATEGORIES ||--o{ TRANSACTIONS : categorizes
    USERS ||--o{ CASHBOOKS : owns
    USERS ||--o{ CASHBOOK_COLLABORATORS : participates
    USERS ||--o{ TRANSACTIONS : creates
    USERS ||--o{ CATEGORIES : creates
```

## Components and Interfaces

### Screen Components

#### AppsScreen
- **Purpose**: Central hub for accessing different app modules
- **Location**: `src/screens/AppsScreen.tsx`
- **Features**:
  - Grid layout of app cards
  - Navigation to CashBook, Recovery Flow, Analytics
  - Consistent with existing app theme
  - Responsive design for mobile and desktop

#### CashbookListScreen
- **Purpose**: Display all cashbooks user has access to
- **Location**: `src/screens/cashbook/CashbookListScreen.tsx`
- **Features**:
  - List of owned and shared cashbooks
  - Create new cashbook button
  - Search and filter capabilities
  - Role-based action visibility

#### CashbookDetailScreen
- **Purpose**: Main cashbook interface with transactions and overview
- **Location**: `src/screens/cashbook/CashbookDetailScreen.tsx`
- **Features**:
  - Financial overview (income, expenses, balance)
  - Transaction list with filtering
  - Add/edit transaction functionality
  - Collaborator management (for owners)

#### TransactionFormScreen
- **Purpose**: Add/edit transaction interface
- **Location**: `src/screens/cashbook/TransactionFormScreen.tsx`
- **Features**:
  - Form with amount, type, category, description, date
  - Category selection with custom category creation
  - Validation and error handling
  - Optimistic updates

### UI Components

#### CashbookCard
- **Purpose**: Display cashbook summary in list view
- **Location**: `src/components/cashbook/CashbookCard.tsx`
- **Props**:
  ```typescript
  interface CashbookCardProps {
    cashbook: Cashbook;
    onPress: () => void;
    userRole: 'owner' | 'editor' | 'viewer';
  }
  ```

#### TransactionItem
- **Purpose**: Display individual transaction in list
- **Location**: `src/components/cashbook/TransactionItem.tsx`
- **Props**:
  ```typescript
  interface TransactionItemProps {
    transaction: Transaction;
    onEdit?: () => void;
    onDelete?: () => void;
    canEdit: boolean;
  }
  ```

#### FinancialOverview
- **Purpose**: Display income, expenses, and balance summary
- **Location**: `src/components/cashbook/FinancialOverview.tsx`
- **Props**:
  ```typescript
  interface FinancialOverviewProps {
    totalIncome: number;
    totalExpenses: number;
    currency: string;
  }
  ```

#### CategorySelector
- **Purpose**: Category selection with custom category creation
- **Location**: `src/components/cashbook/CategorySelector.tsx`
- **Props**:
  ```typescript
  interface CategorySelectorProps {
    selectedCategory?: Category;
    transactionType: 'income' | 'expense';
    onSelect: (category: Category) => void;
    onCreateCustom: (name: string, type: string) => void;
  }
  ```

### Context Providers

#### CashbookContext
- **Purpose**: Global state management for cashbook data
- **Location**: `src/contexts/CashbookContext.tsx`
- **State**:
  ```typescript
  interface CashbookContextState {
    cashbooks: Cashbook[];
    currentCashbook: Cashbook | null;
    transactions: Transaction[];
    categories: Category[];
    loading: boolean;
    error: string | null;
  }
  ```
- **Actions**:
  - `loadCashbooks()`
  - `createCashbook(data: CreateCashbookData)`
  - `updateCashbook(id: string, data: UpdateCashbookData)`
  - `deleteCashbook(id: string)`
  - `loadTransactions(cashbookId: string)`
  - `createTransaction(data: CreateTransactionData)`
  - `updateTransaction(id: string, data: UpdateTransactionData)`
  - `deleteTransaction(id: string)`
  - `inviteCollaborator(cashbookId: string, email: string, role: string)`

### Navigation Structure

```typescript
// Apps Navigator
type AppsStackParamList = {
  AppsList: undefined;
  CashbookStack: undefined;
  RecoveryFlow: undefined;
  Analytics: undefined;
};

// Cashbook Navigator
type CashbookStackParamList = {
  CashbookList: undefined;
  CashbookDetail: { cashbookId: string };
  TransactionForm: { 
    cashbookId: string; 
    transactionId?: string; 
    mode: 'create' | 'edit' 
  };
  CollaboratorManagement: { cashbookId: string };
};
```

## Data Models

### TypeScript Interfaces

```typescript
interface Cashbook {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  currency: string;
  created_at: string;
  updated_at: string;
  user_role?: 'owner' | 'editor' | 'viewer';
  total_income?: number;
  total_expenses?: number;
  current_balance?: number;
}

interface Transaction {
  id: string;
  cashbook_id: string;
  amount: number;
  type: 'income' | 'expense';
  category_id: string;
  description: string;
  date: string;
  created_by: string;
  created_at: string;
  category?: Category;
  created_by_name?: string;
}

interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  is_default: boolean;
  created_by?: string;
  created_at: string;
}

interface CashbookCollaborator {
  id: string;
  cashbook_id: string;
  user_id: string;
  role: 'owner' | 'editor' | 'viewer';
  created_at: string;
  updated_at: string;
  user_name?: string;
  user_email?: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
}
```

### Database Schema

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (assuming exists from auth system)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cashbooks table
CREATE TABLE cashbooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    currency TEXT NOT NULL DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    is_default BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
    amount DECIMAL(12,2) NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    category_id UUID NOT NULL REFERENCES categories(id),
    description TEXT,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cashbook collaborators table
CREATE TABLE cashbook_collaborators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('owner', 'editor', 'viewer')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(cashbook_id, user_id)
);
```

### Row-Level Security Policies

```sql
-- Enable RLS on all tables
ALTER TABLE cashbooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cashbook_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Cashbooks policies
CREATE POLICY "Users can view cashbooks they own or collaborate on" ON cashbooks
    FOR SELECT USING (
        owner_id = auth.uid() OR 
        id IN (
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create their own cashbooks" ON cashbooks
    FOR INSERT WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Only owners can update cashbooks" ON cashbooks
    FOR UPDATE USING (owner_id = auth.uid())
    WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Only owners can delete cashbooks" ON cashbooks
    FOR DELETE USING (owner_id = auth.uid());

-- Transactions policies
CREATE POLICY "Users can view transactions in accessible cashbooks" ON transactions
    FOR SELECT USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE 
            owner_id = auth.uid() OR 
            id IN (
                SELECT cashbook_id FROM cashbook_collaborators 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users with edit access can create transactions" ON transactions
    FOR INSERT WITH CHECK (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = auth.uid()
            UNION
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = auth.uid() AND role IN ('owner', 'editor')
        )
    );

CREATE POLICY "Users with edit access can update transactions" ON transactions
    FOR UPDATE USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = auth.uid()
            UNION
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = auth.uid() AND role IN ('owner', 'editor')
        )
    );

CREATE POLICY "Users with edit access can delete transactions" ON transactions
    FOR DELETE USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = auth.uid()
            UNION
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = auth.uid() AND role IN ('owner', 'editor')
        )
    );

-- Collaborators policies
CREATE POLICY "Users can view collaborators of accessible cashbooks" ON cashbook_collaborators
    FOR SELECT USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE 
            owner_id = auth.uid() OR 
            id IN (
                SELECT cashbook_id FROM cashbook_collaborators 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Only owners can manage collaborators" ON cashbook_collaborators
    FOR ALL USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = auth.uid()
        )
    );

-- Categories policies
CREATE POLICY "Users can view default categories and their own custom categories" ON categories
    FOR SELECT USING (
        is_default = TRUE OR created_by = auth.uid()
    );

CREATE POLICY "Users can create custom categories" ON categories
    FOR INSERT WITH CHECK (created_by = auth.uid() AND is_default = FALSE);
```

## Error Handling

### Error Types

```typescript
enum CashbookErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  DUPLICATE_ERROR = 'DUPLICATE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR'
}

interface CashbookError {
  type: CashbookErrorType;
  message: string;
  field?: string;
  details?: any;
}
```

### Error Handling Strategy

1. **Network Errors**: Retry mechanism with exponential backoff
2. **Validation Errors**: Field-specific error messages with form highlighting
3. **Permission Errors**: Clear messaging with suggested actions
4. **Database Errors**: Graceful degradation with offline capability
5. **Loading States**: Skeleton screens and progress indicators

### Error Boundaries

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class CashbookErrorBoundary extends Component<Props, ErrorBoundaryState> {
  // Error boundary implementation for cashbook screens
}
```

## Testing Strategy

### Unit Testing
- **Components**: Test rendering, props handling, and user interactions
- **Context Providers**: Test state management and action dispatching
- **Utilities**: Test calculation functions and data transformations
- **Validation**: Test form validation logic

### Integration Testing
- **Navigation**: Test screen transitions and parameter passing
- **API Integration**: Test Neon MCP server interactions
- **State Management**: Test context provider integration with components

### End-to-End Testing (Playwright)

```typescript
// Example E2E test scenarios
describe('Cashbook Management E2E', () => {
  test('Create cashbook and add transaction', async ({ page }) => {
    // Navigate to apps screen
    await page.click('[data-testid="apps-menu"]');
    await page.click('[data-testid="cashbook-card"]');
    
    // Create new cashbook
    await page.click('[data-testid="create-cashbook-btn"]');
    await page.fill('[data-testid="cashbook-name"]', 'Test Cashbook');
    await page.selectOption('[data-testid="currency-select"]', 'USD');
    await page.click('[data-testid="save-cashbook-btn"]');
    
    // Add transaction
    await page.click('[data-testid="add-transaction-btn"]');
    await page.fill('[data-testid="amount-input"]', '100.00');
    await page.selectOption('[data-testid="type-select"]', 'income');
    await page.selectOption('[data-testid="category-select"]', 'salary');
    await page.fill('[data-testid="description-input"]', 'Monthly salary');
    await page.click('[data-testid="save-transaction-btn"]');
    
    // Verify financial overview
    await expect(page.locator('[data-testid="total-income"]')).toContainText('$100.00');
    await expect(page.locator('[data-testid="current-balance"]')).toContainText('$100.00');
  });
  
  test('Collaboration workflow', async ({ page, context }) => {
    // Test invitation and permission-based access
  });
  
  test('Mobile responsiveness', async ({ page }) => {
    // Test touch interactions and responsive layouts
  });
});
```

### Performance Testing
- **Database Query Performance**: Test RLS policy efficiency
- **Real-time Updates**: Test collaboration update latency
- **Large Dataset Handling**: Test with thousands of transactions
- **Memory Usage**: Monitor context state and component re-renders

### Security Testing
- **RLS Policy Validation**: Verify data isolation between users
- **Permission Enforcement**: Test role-based access controls
- **Input Sanitization**: Test against SQL injection and XSS
- **Authentication Integration**: Test with existing auth system

## Implementation Considerations

### Performance Optimizations
1. **Lazy Loading**: Load transactions in batches with pagination
2. **Memoization**: Use React.memo for expensive components
3. **Optimistic Updates**: Update UI immediately, sync with server
4. **Caching**: Cache frequently accessed data in context
5. **Database Indexing**: Optimize queries with proper indexes

### Accessibility
1. **Screen Reader Support**: Proper ARIA labels and roles
2. **Keyboard Navigation**: Full keyboard accessibility
3. **Color Contrast**: Meet WCAG guidelines
4. **Touch Targets**: Minimum 44px touch targets for mobile
5. **Focus Management**: Proper focus handling in forms

### Internationalization
1. **Currency Support**: Multiple currency formats and symbols
2. **Date Formats**: Locale-specific date formatting
3. **Number Formats**: Locale-specific number formatting
4. **Text Translation**: Prepare for multi-language support

### Offline Capability
1. **Local Storage**: Cache critical data for offline access
2. **Sync Queue**: Queue operations when offline
3. **Conflict Resolution**: Handle sync conflicts gracefully
4. **Offline Indicators**: Clear offline/online status

This design provides a comprehensive foundation for implementing the Cashbook Management system with proper security, scalability, and user experience considerations.