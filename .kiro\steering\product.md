# Product Overview

This is a React Native mobile application that serves as a comprehensive business management platform with the following key features:

## Core Functionality
- **Task Management**: Kanban-style task tracking with status (todo, in-progress, done) and priority levels (low, medium, high)
- **Authentication System**: Complete user authentication with login, registration, and password recovery
- **Dashboard**: Overview of task statistics, high-priority items, and recent activities
- **Employee Management**: Attendance tracking and payroll management features
- **Settings**: User preferences and application configuration

## User Roles
- **Admin**: Full system access and management capabilities
- **Employee**: Standard user access with task and attendance features
- **User**: Basic access level

## Key Features
- Cross-platform mobile application (iOS/Android)
- Real-time task status updates
- Priority-based task organization
- User role-based access control
- Dark/light theme support
- Responsive design with modern UI components