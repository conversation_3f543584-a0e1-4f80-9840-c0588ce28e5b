# Project Structure

## Root Directory
```
├── .kiro/              # Kiro configuration and steering files
├── node_modules/       # Dependencies
├── public/             # Static assets
├── src/                # Source code
└── styles/             # Global styles
```

## Source Code Organization (`src/`)
```
src/
├── components/         # Reusable UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Header.tsx
│   ├── Input.tsx
│   └── TaskCard.tsx
├── contexts/           # React Context providers
│   ├── AuthContext.tsx
│   └── ThemeContext.tsx
├── navigation/         # Navigation configuration
│   ├── AuthNavigator.tsx
│   ├── MainNavigator.tsx
│   └── RootNavigator.tsx
└── screens/            # Screen components
    ├── auth/           # Authentication screens
    ├── AttendanceScreen.tsx
    ├── DashboardScreen.tsx
    ├── LoadingScreen.tsx
    ├── PayrollScreen.tsx
    └── SettingsScreen.tsx
```

## Component Organization
- **components/**: Reusable UI components that can be used across multiple screens
- **contexts/**: Global state management using React Context API
- **navigation/**: Navigation structure and routing configuration
- **screens/**: Full-screen components representing different app views

## File Naming Conventions
- **PascalCase**: All React components (e.g., `TaskCard.tsx`, `DashboardScreen.tsx`)
- **camelCase**: Utility functions and hooks
- **kebab-case**: CSS files and configuration files

## Import Structure
- **Relative imports**: Used for local components and utilities
- **Absolute imports**: From node_modules and external libraries
- **Type imports**: Using `import type` for TypeScript types

## Screen Categories
- **Auth Screens**: Login, registration, password recovery
- **Main Screens**: Dashboard, task management, settings
- **Utility Screens**: Loading, error states

## Context Providers
- **AuthContext**: User authentication state and methods
- **ThemeContext**: Theme switching and color management

## Navigation Hierarchy
```
RootNavigator
├── AuthNavigator (when not authenticated)
│   └── Auth screens (login, register, etc.)
└── MainNavigator (when authenticated)
    └── Main app screens (dashboard, tasks, etc.)
```

## Styling Organization
- **Global styles**: `styles/globals.css` for Tailwind and CSS variables
- **Component styles**: StyleSheet.create() within each component file
- **Theme variables**: CSS custom properties for consistent theming