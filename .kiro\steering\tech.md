# Technology Stack

## Framework & Platform
- **React Native**: Cross-platform mobile application framework
- **TypeScript**: Primary programming language for type safety
- **React Navigation**: Navigation library for screen transitions and routing

## UI & Styling
- **Tailwind CSS**: Utility-first CSS framework for styling
- **React Native StyleSheet**: Native styling system for component-specific styles
- **CSS Custom Properties**: CSS variables for theming (light/dark mode support)
- **React Native Vector Icons**: Feather icon set for consistent iconography

## State Management & Context
- **React Context API**: Global state management for authentication and theming
- **React Hooks**: useState, useEffect, useContext for local state management
- **Custom Hooks**: useAuth, useTheme for encapsulated logic

## Key Libraries
- **@react-navigation/stack**: Stack-based navigation
- **react-native-safe-area-context**: Safe area handling for different devices
- **react-native-vector-icons/Feather**: Icon library

## Architecture Patterns
- **Context Provider Pattern**: For global state (Auth, Theme)
- **Component Composition**: Reusable UI components
- **Screen-based Architecture**: Separate screens for different app sections
- **Custom Hook Pattern**: Encapsulated business logic

## Project Structure
```
src/
├── components/     # Reusable UI components
├── contexts/       # React Context providers
├── navigation/     # Navigation configuration
└── screens/        # Screen components
```

## Development Conventions
- **"use client"** directive for client-side components
- **Strict TypeScript**: Explicit type definitions for all interfaces
- **Functional Components**: React functional components with hooks
- **StyleSheet.create()**: For component-specific styling
- **Consistent naming**: PascalCase for components, camelCase for functions

## Styling Approach
- **Hybrid Styling**: Combination of Tailwind CSS and React Native StyleSheet
- **Theme System**: CSS custom properties for consistent theming
- **Responsive Design**: Flexible layouts using React Native's Flexbox
- **Color System**: Predefined color palette with light/dark mode variants

## Common Commands
Since no package.json was found in the root, these are typical React Native commands:
```bash
# Development
npm start          # Start Metro bundler
npm run android    # Run on Android
npm run ios        # Run on iOS

# Building
npm run build      # Build for production
```

## Code Style Guidelines
- Use TypeScript interfaces for all prop definitions
- Implement proper error handling with try-catch blocks
- Follow React Native performance best practices
- Use SafeAreaView for proper device compatibility
- Implement loading states for async operations