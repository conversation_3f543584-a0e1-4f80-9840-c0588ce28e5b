import { NextRequest, NextResponse } from 'next/server';
import { CashbookValidation } from '@/lib/cashbook-validation';
import { serverDb } from '@/lib/server-db';

// Import authentication - choose one of these approaches:

// Option 1: NextAuth.js (for production)
// import { getServerSession } from 'next-auth';
// import { authOptions } from '@/lib/auth';

// Option 2: Mock authentication (for development)
import { getServerSession, authOptions } from '@/lib/mock-auth';

// Helper function to get user role for a cashbook
async function getUserRole(cashbookId: string, userId: string): Promise<string | null> {
  try {
    const result = await serverDb.sql`
      SELECT 
        CASE
          WHEN c.owner_id = ${userId} THEN 'owner'
          ELSE COALESCE(cb.role, NULL)
        END as user_role
      FROM cashbooks c
      LEFT JOIN cashbook_collaborators cb ON c.id = cb.cashbook_id AND cb.user_id = ${userId}
      WHERE c.id = ${cashbookId}
    `;

    return result.length > 0 ? result[0].user_role : null;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
}

// GET /api/cashbooks/[cashbookId] - Get specific cashbook
export async function GET(
  request: NextRequest,
  { params }: { params: { cashbookId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { cashbookId } = params;

    // Check user access
    const userRole = await getUserRole(cashbookId, userId);
    if (!userRole) {
      return NextResponse.json(
        { success: false, error: 'Cashbook not found or access denied' },
        { status: 404 }
      );
    }

    // Get cashbook details
    const result = await serverDb.sql`
      SELECT
        c.id,
        c.name,
        c.description,
        c.currency,
        c.created_at,
        c.updated_at,
        c.owner_id,
        COALESCE(fs.total_income, 0) as total_income,
        COALESCE(fs.total_expenses, 0) as total_expenses,
        COALESCE(fs.current_balance, 0) as current_balance,
        COALESCE(fs.transaction_count, 0) as transaction_count,
        u.full_name as owner_name,
        u.email as owner_email
      FROM cashbooks c
      LEFT JOIN financial_summary fs ON c.id = fs.cashbook_id
      LEFT JOIN users u ON c.owner_id = u.id
      WHERE c.id = ${cashbookId}
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cashbook not found' },
        { status: 404 }
      );
    }

    const row = result[0];
    const cashbook = {
      id: row.id,
      name: row.name,
      description: row.description,
      currency: row.currency,
      created_at: row.created_at,
      updated_at: row.updated_at,
      owner_id: row.owner_id,
      user_role: userRole,
      total_income: parseFloat(row.total_income || '0'),
      total_expenses: parseFloat(row.total_expenses || '0'),
      current_balance: parseFloat(row.current_balance || '0'),
      transaction_count: parseInt(row.transaction_count || '0'),
      owner: {
        id: row.owner_id,
        full_name: row.owner_name,
        email: row.owner_email,
      },
    };

    return NextResponse.json({
      success: true,
      data: cashbook,
    });
  } catch (error) {
    console.error('Error fetching cashbook:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch cashbook' },
      { status: 500 }
    );
  }
}

// PUT /api/cashbooks/[cashbookId] - Update cashbook
export async function PUT(
  request: NextRequest,
  { params }: { params: { cashbookId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { cashbookId } = params;

    // Check user access (only owner or editor can update)
    const userRole = await getUserRole(cashbookId, userId);
    if (!userRole || !['owner', 'editor'].includes(userRole)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions to update cashbook' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate input data
    const validation = CashbookValidation.validateUpdateCashbook(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { name, description, currency } = body;

    // Update cashbook
    const result = await serverDb.sql`
      UPDATE cashbooks
      SET name = ${name}, description = ${description || null}, currency = ${currency}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ${cashbookId}
      RETURNING id, name, description, currency, created_at, updated_at, owner_id
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cashbook not found' },
        { status: 404 }
      );
    }

    const updatedCashbook = result[0];

    // Get updated financial summary
    const summaryResult = await serverDb.sql`
      SELECT 
        COALESCE(total_income, 0) as total_income,
        COALESCE(total_expenses, 0) as total_expenses,
        COALESCE(current_balance, 0) as current_balance,
        COALESCE(transaction_count, 0) as transaction_count
      FROM financial_summary 
      WHERE cashbook_id = ${cashbookId}
    `;

    const summary = summaryResult[0] || {
      total_income: '0',
      total_expenses: '0', 
      current_balance: '0',
      transaction_count: '0'
    };

    // Get owner details
    const userResult = await serverDb.sql`
      SELECT full_name, email FROM users WHERE id = ${updatedCashbook.owner_id}
    `;

    const owner = userResult[0];

    const cashbook = {
      id: updatedCashbook.id,
      name: updatedCashbook.name,
      description: updatedCashbook.description,
      currency: updatedCashbook.currency,
      created_at: updatedCashbook.created_at,
      updated_at: updatedCashbook.updated_at,
      owner_id: updatedCashbook.owner_id,
      user_role: userRole,
      total_income: parseFloat(summary.total_income),
      total_expenses: parseFloat(summary.total_expenses),
      current_balance: parseFloat(summary.current_balance),
      transaction_count: parseInt(summary.transaction_count),
      owner: {
        id: updatedCashbook.owner_id,
        full_name: owner?.full_name || '',
        email: owner?.email || '',
      },
    };

    return NextResponse.json({
      success: true,
      data: cashbook,
    });
  } catch (error) {
    console.error('Error updating cashbook:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update cashbook' },
      { status: 500 }
    );
  }
}

// DELETE /api/cashbooks/[cashbookId] - Delete cashbook
export async function DELETE(
  request: NextRequest,
  { params }: { params: { cashbookId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { cashbookId } = params;

    // Check user access (only owner can delete)
    const userRole = await getUserRole(cashbookId, userId);
    if (!userRole || userRole !== 'owner') {
      return NextResponse.json(
        { success: false, error: 'Only the owner can delete this cashbook' },
        { status: 403 }
      );
    }

    // Delete cashbook (cascading deletes will handle related records)
    const result = await serverDb.sql`
      DELETE FROM cashbooks
      WHERE id = ${cashbookId} AND owner_id = ${userId}
      RETURNING id, name
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cashbook not found or already deleted' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Cashbook "${result[0].name}" has been deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting cashbook:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete cashbook' },
      { status: 500 }
    );
  }
}
