import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authOptions } from '@/lib/mock-auth';
import { serverDb } from '@/lib/server-db';
import { z } from 'zod';

// Validation schema for creating transactions
const createTransactionSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  type: z.enum(['income', 'expense'], {
    required_error: 'Transaction type is required',
  }),
  category_id: z.string().uuid('Invalid category ID'),
  description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
  date: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
  payment_method: z.string().optional(),
});

// GET /api/cashbooks/[cashbookId]/transactions - List transactions for a cashbook
export async function GET(
  request: NextRequest,
  { params }: { params: { cashbookId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { cashbookId } = params;
    const userId = session.user.id;

    // Verify user has access to this cashbook
    const cashbook = await serverDb.sql`
      SELECT id FROM cashbooks
      WHERE id = ${cashbookId} AND owner_id = ${userId}
    `;

    if (cashbook.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cashbook not found or access denied' },
        { status: 404 }
      );
    }

    // Get transactions with category information
    const transactions = await serverDb.sql`
      SELECT 
        t.id,
        t.amount,
        t.type,
        t.description,
        t.date,
        t.payment_method,
        t.created_at,
        c.name as category_name,
        c.type as category_type
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE t.cashbook_id = ${cashbookId}
      ORDER BY t.date DESC, t.created_at DESC
    `;

    const formattedTransactions = transactions.map(row => ({
      id: row.id,
      amount: parseFloat(row.amount),
      type: row.type,
      description: row.description,
      date: row.date,
      payment_method: row.payment_method,
      created_at: row.created_at,
      category: {
        name: row.category_name,
        type: row.category_type,
      },
    }));

    return NextResponse.json({
      success: true,
      data: formattedTransactions,
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch transactions' },
      { status: 500 }
    );
  }
}

// POST /api/cashbooks/[cashbookId]/transactions - Create a new transaction
export async function POST(
  request: NextRequest,
  { params }: { params: { cashbookId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { cashbookId } = params;
    const userId = session.user.id;

    // Verify user has access to this cashbook
    const cashbook = await serverDb.sql`
      SELECT id FROM cashbooks
      WHERE id = ${cashbookId} AND owner_id = ${userId}
    `;

    if (cashbook.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cashbook not found or access denied' },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createTransactionSchema.parse(body);

    // Verify category exists and user has access to it
    const category = await serverDb.sql`
      SELECT id, name, type FROM categories 
      WHERE id = ${validatedData.category_id} 
      AND (is_default = true OR created_by = ${userId})
    `;

    if (category.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Category not found or access denied' },
        { status: 404 }
      );
    }

    // Verify category type matches transaction type
    if (category[0].type !== validatedData.type) {
      return NextResponse.json(
        { success: false, error: `Category type (${category[0].type}) does not match transaction type (${validatedData.type})` },
        { status: 400 }
      );
    }

    // Create the transaction
    const newTransaction = await serverDb.sql`
      INSERT INTO transactions (
        cashbook_id,
        amount,
        type,
        category_id,
        description,
        date,
        payment_method,
        created_by
      ) VALUES (
        ${cashbookId},
        ${validatedData.amount},
        ${validatedData.type},
        ${validatedData.category_id},
        ${validatedData.description},
        ${validatedData.date},
        ${validatedData.payment_method || null},
        ${userId}
      )
      RETURNING *
    `;

    if (newTransaction.length === 0) {
      throw new Error('Failed to create transaction');
    }

    // Return the created transaction with category information
    const transactionWithCategory = {
      id: newTransaction[0].id,
      amount: parseFloat(newTransaction[0].amount),
      type: newTransaction[0].type,
      description: newTransaction[0].description,
      date: newTransaction[0].date,
      payment_method: newTransaction[0].payment_method,
      created_at: newTransaction[0].created_at,
      category: {
        id: category[0].id,
        name: category[0].name,
        type: category[0].type,
      },
    };

    return NextResponse.json({
      success: true,
      data: transactionWithCategory,
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating transaction:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create transaction' },
      { status: 500 }
    );
  }
}
