import { NextRequest, NextResponse } from 'next/server';
import { CashbookValidation } from '@/lib/cashbook-validation';
import { serverDb } from '@/lib/server-db';

// Import authentication - choose one of these approaches:

// Option 1: NextAuth.js (for production)
// import { getServerSession } from 'next-auth';
// import { authOptions } from '@/lib/auth';

// Option 2: Mock authentication (for development)
import { getServerSession, authOptions } from '@/lib/mock-auth';

// GET /api/cashbooks - List user's cashbooks
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get cashbooks where user is owner or collaborator
    const result = await serverDb.sql`
      SELECT DISTINCT
        c.id,
        c.name,
        c.description,
        c.currency,
        c.created_at,
        c.updated_at,
        c.owner_id,
        CASE
          WHEN c.owner_id = ${userId} THEN 'owner'
          ELSE COALESCE(cb.role, 'viewer')
        END as user_role,
        COALESCE(fs.total_income, 0) as total_income,
        COALESCE(fs.total_expenses, 0) as total_expenses,
        COALESCE(fs.current_balance, 0) as current_balance,
        COALESCE(fs.transaction_count, 0) as transaction_count,
        u.full_name as owner_name,
        u.email as owner_email
      FROM cashbooks c
      LEFT JOIN cashbook_collaborators cb ON c.id = cb.cashbook_id AND cb.user_id = ${userId}
      LEFT JOIN financial_summary fs ON c.id = fs.cashbook_id
      LEFT JOIN users u ON c.owner_id = u.id
      WHERE c.owner_id = ${userId} OR cb.user_id = ${userId}
      ORDER BY c.updated_at DESC
    `;

    const cashbooks = result.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      currency: row.currency,
      created_at: row.created_at,
      updated_at: row.updated_at,
      owner_id: row.owner_id,
      user_role: row.user_role,
      total_income: parseFloat(row.total_income || '0'),
      total_expenses: parseFloat(row.total_expenses || '0'),
      current_balance: parseFloat(row.current_balance || '0'),
      transaction_count: parseInt(row.transaction_count || '0'),
      owner: {
        id: row.owner_id,
        full_name: row.owner_name,
        email: row.owner_email,
      },
    }));

    return NextResponse.json({
      success: true,
      data: cashbooks,
    });
  } catch (error) {
    console.error('Error fetching cashbooks:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch cashbooks' },
      { status: 500 }
    );
  }
}

// POST /api/cashbooks - Create new cashbook
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const body = await request.json();

    // Validate input data
    const validation = CashbookValidation.validateCreateCashbook(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { name, description, currency } = body;

    // Create cashbook
    const result = await serverDb.sql`
      INSERT INTO cashbooks (name, description, currency, owner_id)
      VALUES (${name}, ${description || null}, ${currency}, ${userId})
      RETURNING id, name, description, currency, created_at, updated_at, owner_id
    `;

    if (result.length === 0) {
      throw new Error('Failed to create cashbook');
    }

    const newCashbook = result[0];

    // Get user details for response
    const userResult = await serverDb.sql`
      SELECT full_name, email FROM users WHERE id = ${userId}
    `;

    const user = userResult[0];

    const cashbook = {
      id: newCashbook.id,
      name: newCashbook.name,
      description: newCashbook.description,
      currency: newCashbook.currency,
      created_at: newCashbook.created_at,
      updated_at: newCashbook.updated_at,
      owner_id: newCashbook.owner_id,
      user_role: 'owner',
      total_income: 0,
      total_expenses: 0,
      current_balance: 0,
      transaction_count: 0,
      owner: {
        id: userId,
        full_name: user?.full_name || '',
        email: user?.email || '',
      },
    };

    return NextResponse.json({
      success: true,
      data: cashbook,
    });
  } catch (error) {
    console.error('Error creating cashbook:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create cashbook' },
      { status: 500 }
    );
  }
}
