import { NextRequest, NextResponse } from 'next/server';
import { CashbookValidation } from '@/lib/cashbook-validation';
import { serverDb } from '@/lib/server-db';

// Import authentication - choose one of these approaches:

// Option 1: NextAuth.js (for production)
// import { getServerSession } from 'next-auth';
// import { authOptions } from '@/lib/auth';

// Option 2: Mock authentication (for development)
import { getServerSession, authOptions } from '@/lib/mock-auth';

// GET /api/categories - List all categories
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    // Build query with filters
    let whereConditions = ['(is_default = true OR created_by = $1)'];
    let queryParams: any[] = [userId];
    let paramIndex = 2;

    if (type && ['income', 'expense'].includes(type)) {
      whereConditions.push(`type = $${paramIndex}`);
      queryParams.push(type);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get categories
    const query = `
      SELECT
        id,
        name,
        type,
        is_default,
        created_by,
        created_at,
        updated_at
      FROM categories
      WHERE ${whereClause}
      ORDER BY is_default DESC, name ASC
    `;

    const result = await serverDb.sql`
      SELECT
        id,
        name,
        type,
        is_default,
        created_by,
        created_at
      FROM categories
      WHERE (is_default = true OR created_by = ${userId})
      ORDER BY is_default DESC, name ASC
    `;

    const categories = result.map(row => ({
      id: row.id,
      name: row.name,
      type: row.type,
      is_default: row.is_default,
      created_by: row.created_by,
      created_at: row.created_at,
    }));

    return NextResponse.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create new category
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const body = await request.json();

    // Validate input data
    const validation = CashbookValidation.validateCreateCategory(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { name, type } = body;

    // Check for duplicate category name within the same type for this user
    const duplicateResult = await serverDb.sql`
      SELECT id FROM categories
      WHERE LOWER(name) = LOWER(${name}) AND type = ${type} AND (is_default = true OR created_by = ${userId})
    `;

    if (duplicateResult.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `A ${type} category with this name already exists` 
        },
        { status: 400 }
      );
    }

    // Create category
    const result = await serverDb.sql`
      INSERT INTO categories (name, type, is_default, created_by)
      VALUES (${name}, ${type}, false, ${userId})
      RETURNING id, name, type, is_default, created_by, created_at, updated_at
    `;

    if (result.length === 0) {
      throw new Error('Failed to create category');
    }

    const newCategory = result[0];

    const category = {
      id: newCategory.id,
      name: newCategory.name,
      type: newCategory.type,
      is_default: newCategory.is_default,
      created_by: newCategory.created_by,
      created_at: newCategory.created_at,
      updated_at: newCategory.updated_at,
    };

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
