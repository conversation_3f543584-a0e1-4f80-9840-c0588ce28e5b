"use client"

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { CashbookProvider, useCashbook } from '@/contexts/CashbookContext';
import CashbookDetailScreen from '@/components/cashbook/CashbookDetailScreen';

interface CashbookDetailPageProps {
  params: {
    id: string;
  };
}

function CashbookDetailContent({ cashbookId }: { cashbookId: string }) {
  const router = useRouter();
  const { cashbooks, selectCashbook, currentCashbook } = useCashbook();

  useEffect(() => {
    // Find and select the cashbook when component mounts
    const cashbook = cashbooks.find(cb => cb.id === cashbookId);
    if (cashbook) {
      selectCashbook(cashbook);
    } else if (cashbooks.length > 0) {
      // If cashbook not found and we have cashbooks loaded, redirect back
      router.push('/apps/cashbook');
    }
  }, [cashbookId, cashbooks, selectCashbook, router]);

  const handleBack = () => {
    router.push('/apps/cashbook');
  };

  const handleEditCashbook = () => {
    // Navigate to edit cashbook (will be implemented later)
    console.log('Edit cashbook:', cashbookId);
  };

  const handleDeleteCashbook = () => {
    // Show delete confirmation (will be implemented later)
    console.log('Delete cashbook:', cashbookId);
  };

  const handleManageCollaborators = () => {
    // Navigate to collaborator management (will be implemented later)
    console.log('Manage collaborators:', cashbookId);
  };

  const handleCreateTransaction = () => {
    // Navigate to create transaction (will be implemented later)
    router.push(`/apps/cashbook/${cashbookId}/transaction/new`);
  };

  const handleEditTransaction = (transactionId: string) => {
    // Navigate to edit transaction (will be implemented later)
    router.push(`/apps/cashbook/${cashbookId}/transaction/${transactionId}/edit`);
  };

  return (
    <CashbookDetailScreen
      cashbookId={cashbookId}
      onBack={handleBack}
      onEditCashbook={handleEditCashbook}
      onDeleteCashbook={handleDeleteCashbook}
      onManageCollaborators={handleManageCollaborators}
      onCreateTransaction={handleCreateTransaction}
      onEditTransaction={handleEditTransaction}
    />
  );
}

export default function CashbookDetailPage({ params }: CashbookDetailPageProps) {
  return (
    <CashbookProvider>
      <div className="min-h-screen bg-background">
        <CashbookDetailContent cashbookId={params.id} />
      </div>
    </CashbookProvider>
  );
}
