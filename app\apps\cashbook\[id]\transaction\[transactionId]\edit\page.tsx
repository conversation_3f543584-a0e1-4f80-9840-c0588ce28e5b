"use client"

import React from 'react';
import { useRouter } from 'next/navigation';
import { CashbookProvider } from '@/contexts/CashbookContext';
import TransactionFormScreen from '@/components/cashbook/TransactionFormScreen';

interface EditTransactionPageProps {
  params: {
    id: string;
    transactionId: string;
  };
}

export default function EditTransactionPage({ params }: EditTransactionPageProps) {
  const router = useRouter();
  const { id: cashbookId, transactionId } = params;

  const handleBack = () => {
    router.push(`/apps/cashbook/${cashbookId}`);
  };

  const handleSave = (transaction: any) => {
    // Navigate back to cashbook detail with success message
    router.push(`/apps/cashbook/${cashbookId}`);
  };

  const handleCancel = () => {
    router.push(`/apps/cashbook/${cashbookId}`);
  };

  return (
    <CashbookProvider>
      <div className="min-h-screen bg-background">
        <TransactionFormScreen
          cashbookId={cashbookId}
          transactionId={transactionId}
          onBack={handleBack}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </div>
    </CashbookProvider>
  );
}
