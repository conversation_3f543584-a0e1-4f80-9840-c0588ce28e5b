"use client"

import React from 'react';
import { CashbookProvider } from '@/contexts/CashbookContext';
import CashbookListScreen from '@/components/cashbook/CashbookListScreen';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { CashbookLayout } from '@/components/layout/AppLayout';

export default function CashbookPage() {
  return (
    <ErrorBoundary>
      <CashbookLayout>
        <CashbookListScreen />
      </CashbookLayout>
    </ErrorBoundary>
  );
}
