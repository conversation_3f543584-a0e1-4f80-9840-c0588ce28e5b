"use client"

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Wallet, 
  CreditCard, 
  BarChart3, 
  Calculator,
  ArrowRight,
  Sparkles,
  TrendingUp,
  Users,
  Shield,
  Clock
} from 'lucide-react';
import { useAuth } from '@/components/auth-provider';

interface AppCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  status: 'available' | 'coming-soon' | 'beta';
  features: string[];
  color: string;
  disabled?: boolean;
}

function AppCard({ title, description, icon, href, status, features, color, disabled = false }: AppCardProps) {
  const cardContent = (
    <Card className={`group transition-all duration-300 hover:shadow-lg hover:scale-105 ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'} border-2 hover:border-primary/20`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className={`p-3 rounded-lg ${color} group-hover:scale-110 transition-transform duration-300`}>
            {icon}
          </div>
          <div className="flex flex-col items-end gap-1">
            <Badge 
              variant={status === 'available' ? 'default' : status === 'beta' ? 'secondary' : 'outline'}
              className="text-xs"
            >
              {status === 'available' ? 'Available' : status === 'beta' ? 'Beta' : 'Coming Soon'}
            </Badge>
          </div>
        </div>
        <CardTitle className="text-xl font-semibold group-hover:text-primary transition-colors">
          {title}
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="space-y-1">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-1.5 h-1.5 rounded-full bg-primary/60" />
                {feature}
              </div>
            ))}
          </div>
          <div className="flex items-center justify-between pt-2">
            <span className="text-sm font-medium text-primary">
              {status === 'available' ? 'Launch App' : status === 'beta' ? 'Try Beta' : 'Notify Me'}
            </span>
            <ArrowRight className="w-4 h-4 text-primary group-hover:translate-x-1 transition-transform" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (disabled || status === 'coming-soon') {
    return <div className="relative">{cardContent}</div>;
  }

  return (
    <Link href={href} className="block">
      {cardContent}
    </Link>
  );
}

export default function AppsScreen() {
  const { user } = useAuth();

  const apps: AppCardProps[] = [
    {
      title: 'CashBook',
      description: 'Comprehensive financial tracking and management system with real-time collaboration.',
      icon: <Wallet className="w-6 h-6 text-white" />,
      href: '/apps/cashbook',
      status: 'available',
      color: 'bg-gradient-to-br from-green-500 to-emerald-600',
      features: [
        'Multi-currency support',
        'Real-time collaboration',
        'Transaction categorization',
        'Financial reporting',
        'Expense tracking'
      ]
    },
    {
      title: 'Recovery Flow',
      description: 'Advanced loan recovery and customer management system with automated workflows.',
      icon: <CreditCard className="w-6 h-6 text-white" />,
      href: '/recovery-flow',
      status: 'available',
      color: 'bg-gradient-to-br from-blue-500 to-cyan-600',
      features: [
        'Customer management',
        'Payment tracking',
        'Automated reminders',
        'Recovery analytics',
        'Communication logs'
      ]
    },
    {
      title: 'Analytics',
      description: 'Powerful business intelligence and reporting platform with interactive dashboards.',
      icon: <BarChart3 className="w-6 h-6 text-white" />,
      href: '/analytics',
      status: 'available',
      color: 'bg-gradient-to-br from-purple-500 to-violet-600',
      features: [
        'Interactive dashboards',
        'Custom reports',
        'Data visualization',
        'Performance metrics',
        'Export capabilities'
      ]
    },
    {
      title: 'Smart Calculator',
      description: 'Advanced financial calculator with loan calculations and investment planning.',
      icon: <Calculator className="w-6 h-6 text-white" />,
      href: '/calculator',
      status: 'available',
      color: 'bg-gradient-to-br from-orange-500 to-red-600',
      features: [
        'Loan calculations',
        'Investment planning',
        'Tax calculations',
        'Currency conversion',
        'Financial formulas'
      ]
    },
    {
      title: 'Project Manager',
      description: 'Comprehensive project and task management with team collaboration features.',
      icon: <Users className="w-6 h-6 text-white" />,
      href: '/apps/projects',
      status: 'beta',
      color: 'bg-gradient-to-br from-indigo-500 to-blue-600',
      features: [
        'Task management',
        'Team collaboration',
        'Progress tracking',
        'Time logging',
        'Resource planning'
      ]
    },
    {
      title: 'Security Center',
      description: 'Advanced security monitoring and compliance management system.',
      icon: <Shield className="w-6 h-6 text-white" />,
      href: '/apps/security',
      status: 'coming-soon',
      color: 'bg-gradient-to-br from-gray-500 to-slate-600',
      disabled: true,
      features: [
        'Security monitoring',
        'Compliance tracking',
        'Audit logs',
        'Risk assessment',
        'Access control'
      ]
    }
  ];

  // Filter apps based on user role
  const availableApps = apps.filter(app => {
    if (app.title === 'Recovery Flow' && user && !['admin', 'hr_manager'].includes(user.role)) {
      return false;
    }
    return true;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="w-8 h-8 text-primary" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              App Center
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover powerful tools and applications designed to streamline your business operations
            and boost productivity.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-500" />
                <span className="text-2xl font-bold">{availableApps.filter(app => app.status === 'available').length}</span>
              </div>
              <p className="text-sm text-muted-foreground">Available Apps</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Clock className="w-5 h-5 text-blue-500" />
                <span className="text-2xl font-bold">{availableApps.filter(app => app.status === 'beta').length}</span>
              </div>
              <p className="text-sm text-muted-foreground">Beta Apps</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Sparkles className="w-5 h-5 text-purple-500" />
                <span className="text-2xl font-bold">{availableApps.filter(app => app.status === 'coming-soon').length}</span>
              </div>
              <p className="text-sm text-muted-foreground">Coming Soon</p>
            </CardContent>
          </Card>
        </div>

        {/* Apps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {availableApps.map((app, index) => (
            <AppCard key={index} {...app} />
          ))}
        </div>

        {/* Footer */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
            <CardContent className="pt-6">
              <h3 className="text-lg font-semibold mb-2">Need a Custom Solution?</h3>
              <p className="text-muted-foreground mb-4">
                Contact our development team to discuss custom applications tailored to your specific needs.
              </p>
              <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-primary-foreground">
                Contact Development Team
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
