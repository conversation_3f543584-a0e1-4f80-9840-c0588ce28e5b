"use client"

import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  is_default: boolean;
}

export default function TestCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadCategories() {
      try {
        setLoading(true);
        const response = await fetch('/api/categories');
        const result = await response.json();

        if (result.success) {
          setCategories(result.data);
        } else {
          setError(result.error || 'Failed to load categories');
        }
      } catch (err) {
        setError('Network error loading categories');
        console.error('Error loading categories:', err);
      } finally {
        setLoading(false);
      }
    }

    loadCategories();
  }, []);

  const incomeCategories = categories.filter(c => c.type === 'income');
  const expenseCategories = categories.filter(c => c.type === 'expense');

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading categories...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-red-600 mb-4">❌ Error</div>
            <p>{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Categories Test Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Income Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">
              💰 Income Categories ({incomeCategories.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {incomeCategories.length > 0 ? (
              <div className="space-y-2">
                {incomeCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center justify-between p-2 bg-green-50 rounded"
                  >
                    <span className="font-medium">{category.name}</span>
                    <span className="text-xs text-gray-500">
                      {category.is_default ? 'Default' : 'Custom'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No income categories found</p>
            )}
          </CardContent>
        </Card>

        {/* Expense Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">
              💸 Expense Categories ({expenseCategories.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {expenseCategories.length > 0 ? (
              <div className="space-y-2">
                {expenseCategories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center justify-between p-2 bg-red-50 rounded"
                  >
                    <span className="font-medium">{category.name}</span>
                    <span className="text-xs text-gray-500">
                      {category.is_default ? 'Default' : 'Custom'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No expense categories found</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Summary */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>📊 Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{incomeCategories.length}</div>
              <div className="text-sm text-gray-600">Income Categories</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{expenseCategories.length}</div>
              <div className="text-sm text-gray-600">Expense Categories</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{categories.length}</div>
              <div className="text-sm text-gray-600">Total Categories</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {categories.filter(c => c.is_default).length}
              </div>
              <div className="text-sm text-gray-600">Default Categories</div>
            </div>
          </div>
          
          <div className="mt-4 p-4 bg-blue-50 rounded">
            <h4 className="font-semibold mb-2">✅ Expected Results for Cash In/Out:</h4>
            <ul className="text-sm space-y-1">
              <li>• Cash In modal should show {incomeCategories.length} income categories</li>
              <li>• Cash Out modal should show {expenseCategories.length} expense categories</li>
              <li>• Categories should be properly filtered by transaction type</li>
              <li>• Users should be able to select categories and create transactions</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
