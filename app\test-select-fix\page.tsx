"use client"

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Mock categories for testing
const mockCategories = [
  { id: '1', name: 'Salary', type: 'income' as const },
  { id: '2', name: 'Freelance', type: 'income' as const },
  { id: '3', name: 'Food & Dining', type: 'expense' as const },
  { id: '4', name: 'Transportation', type: 'expense' as const },
];

export default function TestSelectFixPage() {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [transactionType, setTransactionType] = useState<'income' | 'expense'>('income');
  const [simulateEmpty, setSimulateEmpty] = useState(false);

  // Filter categories based on transaction type and simulation mode
  const filteredCategories = simulateEmpty 
    ? [] 
    : mockCategories.filter(cat => cat.type === transactionType);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">React Select Component Fix Test</h1>
      
      {/* Test Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>🎛️ Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button
              onClick={() => setTransactionType('income')}
              variant={transactionType === 'income' ? 'default' : 'outline'}
            >
              Income Categories
            </Button>
            <Button
              onClick={() => setTransactionType('expense')}
              variant={transactionType === 'expense' ? 'default' : 'outline'}
            >
              Expense Categories
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="simulate-empty"
              checked={simulateEmpty}
              onChange={(e) => setSimulateEmpty(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="simulate-empty" className="text-sm">
              Simulate empty categories (test edge case)
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Select Component Test */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>
            📋 Category Select Test - {transactionType} 
            {simulateEmpty && ' (Empty)'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Category</label>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {filteredCategories.length > 0 ? (
                  filteredCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-categories-available" disabled>
                    No {transactionType} categories available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div className="p-3 bg-gray-100 rounded">
            <p className="text-sm">
              <strong>Selected Value:</strong> {selectedCategory || 'None'}
            </p>
            <p className="text-sm">
              <strong>Categories Available:</strong> {filteredCategories.length}
            </p>
            <p className="text-sm">
              <strong>Valid Selection:</strong> {
                selectedCategory && selectedCategory !== 'no-categories-available' 
                  ? '✅ Yes' 
                  : '❌ No'
              }
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>✅ Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span className="text-sm">
                Select component renders without errors
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span className="text-sm">
                No "empty string value" console errors
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span className="text-sm">
                Placeholder item uses "no-categories-available" value
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span className="text-sm">
                Placeholder item is disabled and cannot be selected
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-green-600">✅</span>
              <span className="text-sm">
                Normal category selection works when categories are available
              </span>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <h4 className="font-semibold text-blue-800 mb-2">🧪 Testing Instructions:</h4>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. Toggle between Income and Expense categories</li>
              <li>2. Check "Simulate empty categories" to test edge case</li>
              <li>3. Try to select categories in both modes</li>
              <li>4. Check browser console for any React Select errors</li>
              <li>5. Verify that placeholder cannot be selected when checked</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
