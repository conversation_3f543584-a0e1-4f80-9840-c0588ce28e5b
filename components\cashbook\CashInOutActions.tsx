"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  TrendingUp,
  TrendingDown,
  Loader2,
  Calendar,
  CreditCard,
  Banknote,
  Building2,
  Smartphone,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface CashInOutActionsProps {
  cashbookId: string;
  currency: string;
  onCreateTransaction: (data: {
    amount: number;
    type: 'income' | 'expense';
    category_id: string;
    description: string;
    date: string;
    payment_method?: string;
  }) => Promise<void>;
  categories: Array<{
    id: string;
    name: string;
    type: 'income' | 'expense';
    is_default: boolean;
  }>;
  disabled?: boolean;
  className?: string;
}

interface TransactionFormData {
  amount: string;
  description: string;
  category_id: string;
  date: string;
  payment_method: string;
}

const PAYMENT_METHODS = [
  { value: 'cash', label: 'Cash', icon: <Banknote className="w-4 h-4" /> },
  { value: 'bank_transfer', label: 'Bank Transfer', icon: <Building2 className="w-4 h-4" /> },
  { value: 'card', label: 'Card', icon: <CreditCard className="w-4 h-4" /> },
  { value: 'mobile_payment', label: 'Mobile Payment', icon: <Smartphone className="w-4 h-4" /> },
];

export default function CashInOutActions({
  cashbookId,
  currency,
  onCreateTransaction,
  categories,
  disabled = false,
  className = '',
}: CashInOutActionsProps) {
  const { toast } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [transactionType, setTransactionType] = useState<'income' | 'expense'>('income');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState<TransactionFormData>({
    amount: '',
    description: '',
    category_id: '',
    date: new Date().toISOString().split('T')[0], // Today's date
    payment_method: 'cash',
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isModalOpen) {
      setFormData({
        amount: '',
        description: '',
        category_id: '',
        date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
      });
      setErrors({});
    }
  }, [isModalOpen]);

  // Filter categories based on transaction type
  const filteredCategories = categories.filter(cat => cat.type === transactionType);



  const handleOpenModal = (type: 'income' | 'expense') => {
    setTransactionType(type);
    setIsModalOpen(true);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate amount
    const amount = parseFloat(formData.amount);
    if (!formData.amount || isNaN(amount) || amount <= 0) {
      newErrors.amount = 'Please enter a valid positive amount';
    }

    // Validate description
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    // Validate category
    if (!formData.category_id || formData.category_id === 'no-categories-available') {
      newErrors.category_id = 'Please select a category';
    }

    // Validate date
    if (!formData.date) {
      newErrors.date = 'Date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    // Additional validation for categories
    if (filteredCategories.length === 0) {
      setErrors(prev => ({
        ...prev,
        category_id: `No ${transactionType} categories available. Please create a category first.`
      }));
      return;
    }

    setIsSubmitting(true);

    try {
      await onCreateTransaction({
        amount: parseFloat(formData.amount),
        type: transactionType,
        category_id: formData.category_id,
        description: formData.description.trim(),
        date: formData.date,
        payment_method: formData.payment_method,
      });

      // Show success toast
      const categoryName = filteredCategories.find(cat => cat.id === formData.category_id)?.name || 'Unknown';
      toast({
        title: "Transaction Created Successfully",
        description: `${transactionType === 'income' ? 'Cash In' : 'Cash Out'} of ${currency} ${formData.amount} for ${categoryName} has been recorded.`,
        variant: "default",
      });

      // Clear form data
      setFormData({
        amount: '',
        description: '',
        category_id: '',
        date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
      });

      // Clear any errors
      setErrors({});

      // Close modal on success
      setIsModalOpen(false);
    } catch (error) {
      console.error('Failed to create transaction:', error);

      // Show error toast
      toast({
        title: "Transaction Failed",
        description: error instanceof Error ? error.message : 'Failed to create transaction. Please try again.',
        variant: "destructive",
      });

      // Set a user-friendly error message
      setErrors(prev => ({
        ...prev,
        submit: 'Failed to create transaction. Please try again.'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const updateFormData = (field: keyof TransactionFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className={cn("", className)}>
      {/* Cash In and Cash Out Buttons */}
      <div className={cn(
        "flex gap-2 max-md:gap-3 md:static md:z-auto",
        // Hide on mobile when modal is open, always show on desktop
        isModalOpen ? "max-md:hidden" : "max-md:fixed max-md:bottom-20 max-md:left-4 max-md:right-4 max-md:z-[60]"
      )}>
        <Button
          onClick={() => handleOpenModal('income')}
          disabled={disabled}
          className="flex-1 h-12 flex items-center justify-center gap-2 text-sm font-semibold min-h-[44px] touch-manipulation bg-green-600 text-white hover:bg-green-700 md:flex-none md:h-10 md:px-8"
        >
          <TrendingUp className="w-4 h-4 md:w-5 md:h-5" />
          Cash In
        </Button>

        <Button
          onClick={() => handleOpenModal('expense')}
          disabled={disabled}
          className="flex-1 h-12 flex items-center justify-center gap-2 text-sm font-semibold min-h-[44px] touch-manipulation bg-red-600 text-white hover:bg-red-700 md:flex-none md:h-10 md:px-8"
        >
          <TrendingDown className="w-4 h-4 md:w-5 md:h-5" />
          Cash Out
        </Button>
      </div>

      {/* Transaction Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="w-[95vw] max-w-md max-h-[95vh] sm:max-h-[90vh] flex flex-col overflow-hidden p-4 sm:p-6">
          <DialogHeader className="flex-shrink-0 pb-4">
            <DialogTitle className="flex items-center gap-2">
              {transactionType === 'income' ? (
                <TrendingUp className="w-5 h-5 text-green-600" />
              ) : (
                <TrendingDown className="w-5 h-5 text-red-600" />
              )}
              {transactionType === 'income' ? 'Cash In' : 'Cash Out'}
            </DialogTitle>
            <DialogDescription>
              Record a new {transactionType === 'income' ? 'income' : 'expense'} transaction
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto px-1 -mx-1">
            <div className="space-y-5 pb-4 px-1 sm:space-y-4">
            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount">Amount *</Label>
              <div className="relative">
                <span className="absolute left-3 top-3 text-muted-foreground text-sm">
                  {currency}
                </span>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={(e) => updateFormData('amount', e.target.value)}
                  disabled={isSubmitting}
                  className={cn("pl-16 h-12 sm:h-10", errors.amount && "border-red-500")}
                  autoFocus
                />
              </div>
              {errors.amount && (
                <p className="text-sm text-red-500">{errors.amount}</p>
              )}
            </div>
            
            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                placeholder="What is this transaction for?"
                value={formData.description}
                onChange={(e) => updateFormData('description', e.target.value)}
                disabled={isSubmitting}
                className={cn("min-h-[48px] sm:min-h-[80px]", errors.description && "border-red-500")}
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) => updateFormData('category_id', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={cn("h-12 sm:h-10", errors.category_id && "border-red-500")}>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {filteredCategories.length > 0 ? (
                    filteredCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-categories-available" disabled>
                      No {transactionType} categories available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.category_id && (
                <p className="text-sm text-red-500">{errors.category_id}</p>
              )}
            </div>

            {/* Date */}
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => updateFormData('date', e.target.value)}
                  disabled={isSubmitting}
                  className={cn("pl-10 h-12 sm:h-10", errors.date && "border-red-500")}
                />
              </div>
              {errors.date && (
                <p className="text-sm text-red-500">{errors.date}</p>
              )}
            </div>

            {/* Payment Method */}
            <div className="space-y-2">
              <Label htmlFor="payment-method">Payment Method</Label>
              <Select
                value={formData.payment_method}
                onValueChange={(value) => updateFormData('payment_method', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger className="h-12 sm:h-10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_METHODS.map((method) => (
                    <SelectItem key={method.value} value={method.value}>
                      <div className="flex items-center gap-2">
                        {method.icon}
                        {method.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            </div>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-3 bg-red-100 border border-red-400 rounded">
              <p className="text-sm text-red-700">{errors.submit}</p>
            </div>
          )}

          <DialogFooter className="flex-shrink-0 pt-4 border-t gap-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="min-h-[44px] flex-1 sm:flex-none"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={
                isSubmitting ||
                !formData.amount ||
                !formData.description ||
                !formData.category_id ||
                formData.category_id === 'no-categories-available' ||
                filteredCategories.length === 0
              }
              className="flex items-center gap-2 min-h-[44px] flex-1 sm:flex-none"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  {transactionType === 'income' ? (
                    <TrendingUp className="w-4 h-4" />
                  ) : (
                    <TrendingDown className="w-4 h-4" />
                  )}
                  Confirm
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
