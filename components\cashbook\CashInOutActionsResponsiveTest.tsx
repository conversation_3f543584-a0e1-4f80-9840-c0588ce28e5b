"use client"

import React from 'react';
import CashInOutActions from './CashInOutActions';

// Mock data for testing
const mockCategories = [
  { id: '1', name: 'Sal<PERSON>', type: 'income' as const, is_default: true },
  { id: '2', name: 'Freelance', type: 'income' as const, is_default: false },
  { id: '3', name: 'Food & Dining', type: 'expense' as const, is_default: true },
  { id: '4', name: 'Transportation', type: 'expense' as const, is_default: false },
];

const mockOnCreateTransaction = async (data: any) => {
  console.log('Mock transaction created:', data);
  alert(`Transaction created: ${data.type} of ${data.amount} for ${data.description}`);
};

export default function CashInOutActionsResponsiveTest() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Cash In/Out Actions Responsive Test</h1>
          <p className="text-gray-600 mb-8">
            Test the responsive behavior of Cash In/Out buttons across different screen sizes
          </p>
        </div>

        {/* Test Instructions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-green-600 mb-2">Mobile/Small Screens (&lt; 768px)</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Buttons should be in a single horizontal row</li>
                <li>• Buttons should be compact/small size</li>
                <li>• Buttons should have solid green/red backgrounds with white text</li>
                <li>• No "Add Transaction" button should be visible</li>
                <li>• Buttons should be positioned at bottom of screen</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-blue-600 mb-2">Desktop/Large Screens (≥ 768px)</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Buttons should be in a single horizontal row</li>
                <li>• Buttons should match original "Add Transaction" button size</li>
                <li>• Buttons should have solid green/red backgrounds</li>
                <li>• Buttons should be positioned normally in layout</li>
                <li>• Both buttons should have identical sizing</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Component Test */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Component Test</h2>
          <CashInOutActions
            cashbookId="test-cashbook-id"
            currency="USD"
            categories={mockCategories}
            onCreateTransaction={mockOnCreateTransaction}
            disabled={false}
          />
        </div>

        {/* Screen Size Indicator */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Current Screen Size</h2>
          <div className="flex items-center gap-4">
            <div className="block md:hidden px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Mobile View (&lt; 768px)
            </div>
            <div className="hidden md:block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              Desktop View (≥ 768px)
            </div>
          </div>
        </div>

        {/* Resize Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <p className="text-yellow-800 text-sm">
            <strong>Testing Tip:</strong> Resize your browser window or use browser dev tools to test different screen sizes.
            The buttons should change their appearance and positioning based on the screen size.
          </p>
        </div>
      </div>
    </div>
  );
}
