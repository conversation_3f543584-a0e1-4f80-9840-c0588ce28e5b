"use client"

import React from 'react';
import CashInOutActions from './CashInOutActions';

// Test component to verify CashInOutActions renders
export default function CashInOutActionsTest() {
  const mockCategories = [
    { id: '1', name: 'Salary', type: 'income' as const, is_default: true },
    { id: '2', name: 'Food & Dining', type: 'expense' as const, is_default: true },
  ];

  const mockOnCreateTransaction = async (data: any) => {
    console.log('Mock transaction creation:', data);
    alert('Mock transaction created: ' + JSON.stringify(data, null, 2));
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Cash In/Out Actions Test</h1>
      
      <div className="border border-gray-300 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">Component Test:</h2>
        
        <CashInOutActions
          cashbookId="test-cashbook-id"
          currency="USD"
          categories={mockCategories}
          onCreateTransaction={mockOnCreateTransaction}
          disabled={false}
        />
      </div>
      
      <div className="mt-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Expected Behavior:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Two buttons should appear: "Cash In" (green) and "Cash Out" (red)</li>
          <li>Clicking either button should open a modal with a transaction form</li>
          <li>Form should have fields for amount, description, category, date, and payment method</li>
          <li>Submitting the form should show an alert with the transaction data</li>
        </ul>
      </div>
    </div>
  );
}
