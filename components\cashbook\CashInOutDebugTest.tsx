"use client"

import React from 'react';
import CashInOutActions from './CashInOutActions';

// Mock data for testing
const mockCategories = [
  { id: '1', name: 'Sal<PERSON>', type: 'income' as const, is_default: true },
  { id: '2', name: 'Freelance', type: 'income' as const, is_default: false },
  { id: '3', name: 'Food & Dining', type: 'expense' as const, is_default: true },
  { id: '4', name: 'Transportation', type: 'expense' as const, is_default: false },
];

const mockOnCreateTransaction = async (data: any) => {
  console.log('Mock transaction created:', data);
  alert(`Transaction created: ${data.type} of ${data.amount} for ${data.description}`);
};

export default function CashInOutDebugTest() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Cash In/Out Debug Test</h1>
          <p className="text-gray-600 mb-8">
            Testing if the CashInOutActions component renders and functions correctly
          </p>
        </div>

        {/* Component Test */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Component Rendering Test</h2>
          <p className="text-gray-600 mb-4">
            The buttons should appear below. If they don't, there's a rendering issue.
          </p>
          
          {/* Test the component directly */}
          <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
            <p className="text-sm text-gray-500 mb-2">Component should render here:</p>
            <CashInOutActions
              cashbookId="test-cashbook-id"
              currency="USD"
              categories={mockCategories}
              onCreateTransaction={mockOnCreateTransaction}
              disabled={false}
              className="border border-red-200 bg-red-50 p-2"
            />
          </div>
        </div>

        {/* Debug Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <div className="space-y-2 text-sm">
            <p><strong>Categories provided:</strong> {mockCategories.length} categories</p>
            <p><strong>Disabled state:</strong> false</p>
            <p><strong>Currency:</strong> USD</p>
            <p><strong>Cashbook ID:</strong> test-cashbook-id</p>
          </div>
        </div>

        {/* Manual Button Test */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Manual Button Test</h2>
          <p className="text-gray-600 mb-4">
            These are manual buttons with the same styling to test if the issue is with the component or styling:
          </p>
          
          <div className="flex gap-2 max-md:fixed max-md:bottom-20 max-md:left-4 max-md:right-4 max-md:z-[60] max-md:gap-3 md:static md:z-auto">
            <button className="flex-1 h-12 flex items-center justify-center gap-2 text-sm font-semibold min-h-[44px] touch-manipulation bg-green-600 text-white hover:bg-green-700 md:flex-none md:h-10 md:px-8 rounded-md">
              <span>📈</span>
              Manual Cash In
            </button>
            <button className="flex-1 h-12 flex items-center justify-center gap-2 text-sm font-semibold min-h-[44px] touch-manipulation bg-red-600 text-white hover:bg-red-700 md:flex-none md:h-10 md:px-8 rounded-md">
              <span>📉</span>
              Manual Cash Out
            </button>
          </div>
        </div>

        {/* Screen Size Indicator */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Current Screen Size</h2>
          <div className="flex items-center gap-4">
            <div className="block md:hidden px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Mobile View (&lt; 768px)
            </div>
            <div className="hidden md:block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              Desktop View (≥ 768px)
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
