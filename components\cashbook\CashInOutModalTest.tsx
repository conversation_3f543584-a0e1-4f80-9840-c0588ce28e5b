"use client"

import React from 'react';
import CashInOutActions from './CashInOutActions';

// Mock data for testing
const mockCategories = [
  { id: '1', name: 'Salary', type: 'income' as const, is_default: true },
  { id: '2', name: 'Freelance Work', type: 'income' as const, is_default: false },
  { id: '3', name: 'Investment Returns', type: 'income' as const, is_default: false },
  { id: '4', name: 'Food & Dining', type: 'expense' as const, is_default: true },
  { id: '5', name: 'Transportation', type: 'expense' as const, is_default: false },
  { id: '6', name: 'Entertainment', type: 'expense' as const, is_default: false },
  { id: '7', name: 'Utilities', type: 'expense' as const, is_default: false },
  { id: '8', name: 'Healthcare', type: 'expense' as const, is_default: false },
];

const mockOnCreateTransaction = async (data: any) => {
  console.log('Mock transaction created:', data);
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  alert(`Transaction created: ${data.type} of ${data.amount} for ${data.description}`);
};

export default function CashInOutModalTest() {
  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Cash In/Out Modal Responsive Test</h1>
          <p className="text-gray-600 mb-8">
            Test the responsive modal behavior and form usability across different screen sizes
          </p>
        </div>

        {/* Test Instructions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Modal Responsive Features</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-blue-600 mb-2">Mobile/Small Screens (&lt; 768px)</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Modal fits within viewport (95vh max height)</li>
                <li>• Form content is scrollable when needed</li>
                <li>• Input fields have 48px height for touch targets</li>
                <li>• Buttons are full-width with 44px minimum height</li>
                <li>• Increased spacing between form fields (20px)</li>
                <li>• Footer buttons are sticky at bottom</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-green-600 mb-2">Desktop/Large Screens (≥ 768px)</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Modal uses standard sizing (max-width: 448px)</li>
                <li>• Input fields use standard 40px height</li>
                <li>• Buttons use standard sizing</li>
                <li>• Standard spacing between form fields (16px)</li>
                <li>• Modal height limited to 90vh</li>
                <li>• Proper scrolling when content overflows</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Component Test */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Interactive Test</h2>
          <p className="text-gray-600 mb-4">
            Click the buttons below to test the modal on your current screen size:
          </p>
          <CashInOutActions
            cashbookId="test-cashbook-id"
            currency="USD"
            categories={mockCategories}
            onCreateTransaction={mockOnCreateTransaction}
            disabled={false}
          />
        </div>

        {/* Test Checklist */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Test Checklist</h2>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <input type="checkbox" className="mt-1" />
              <label className="text-sm">
                <strong>Modal Height:</strong> Modal fits within viewport without being cut off
              </label>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" className="mt-1" />
              <label className="text-sm">
                <strong>Scrolling:</strong> Form content scrolls when modal height is exceeded
              </label>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" className="mt-1" />
              <label className="text-sm">
                <strong>Touch Targets:</strong> All form fields and buttons are easily tappable on mobile
              </label>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" className="mt-1" />
              <label className="text-sm">
                <strong>Form Fields:</strong> All fields (amount, description, category, date, payment) are accessible
              </label>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" className="mt-1" />
              <label className="text-sm">
                <strong>Footer Buttons:</strong> Cancel and Confirm buttons are always visible and accessible
              </label>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" className="mt-1" />
              <label className="text-sm">
                <strong>Responsive Behavior:</strong> Modal adapts properly when resizing browser window
              </label>
            </div>
          </div>
        </div>

        {/* Screen Size Indicator */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Current Screen Size</h2>
          <div className="flex items-center gap-4">
            <div className="block sm:hidden px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              Extra Small (&lt; 640px)
            </div>
            <div className="hidden sm:block md:hidden px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Small (640px - 767px)
            </div>
            <div className="hidden md:block lg:hidden px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
              Medium (768px - 1023px)
            </div>
            <div className="hidden lg:block xl:hidden px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
              Large (1024px - 1279px)
            </div>
            <div className="hidden xl:block px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
              Extra Large (≥ 1280px)
            </div>
          </div>
        </div>

        {/* Testing Tips */}
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
          <p className="text-blue-800 text-sm">
            <strong>Testing Tips:</strong> 
            <br />• Use browser dev tools to simulate different device sizes
            <br />• Test on actual mobile devices if available
            <br />• Try filling out the form completely to test all interactions
            <br />• Test both Cash In (income) and Cash Out (expense) modals
            <br />• Verify that form validation works properly
          </p>
        </div>
      </div>
    </div>
  );
}
