"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Wallet, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  MoreVertical,
  Edit,
  Trash2,
  UserPlus,
  Eye
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface CashbookCardProps {
  cashbook: {
    id: string;
    name: string;
    description?: string;
    currency: string;
    user_role?: 'owner' | 'editor' | 'viewer';
    total_income?: number;
    total_expenses?: number;
    current_balance?: number;
    transaction_count?: number;
    owner?: {
      id: string;
      full_name: string;
      email: string;
    };
  };
  onPress: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onManageCollaborators?: () => void;
  className?: string;
}

// Currency symbols mapping
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  NPR: 'Rs.',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
};

function formatCurrency(amount: number, currency: string): string {
  const symbol = CURRENCY_SYMBOLS[currency] || currency;
  const isNegative = amount < 0;
  const absoluteAmount = Math.abs(amount);
  
  const formatted = absoluteAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  const result = `${symbol}${formatted}`;
  return isNegative ? `-${result}` : result;
}

function getRoleColor(role: string): string {
  switch (role) {
    case 'owner':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'editor':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'viewer':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

function getRoleIcon(role: string) {
  switch (role) {
    case 'owner':
      return <Wallet className="w-3 h-3" />;
    case 'editor':
      return <Edit className="w-3 h-3" />;
    case 'viewer':
      return <Eye className="w-3 h-3" />;
    default:
      return <Users className="w-3 h-3" />;
  }
}

export default function CashbookCard({
  cashbook,
  onPress,
  onEdit,
  onDelete,
  onManageCollaborators,
  className = '',
}: CashbookCardProps) {
  const {
    name,
    description,
    currency,
    user_role = 'viewer',
    total_income = 0,
    total_expenses = 0,
    current_balance = 0,
    transaction_count = 0,
    owner,
  } = cashbook;

  const isOwner = user_role === 'owner';
  const canEdit = user_role === 'owner' || user_role === 'editor';
  const isPositiveBalance = current_balance >= 0;

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on dropdown menu
    if ((e.target as HTMLElement).closest('[data-dropdown-trigger]')) {
      return;
    }
    onPress();
  };

  const handleMenuAction = (action: () => void) => {
    return (e: React.MouseEvent) => {
      e.stopPropagation();
      action();
    };
  };

  return (
    <Card 
      className={`group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] border-2 hover:border-primary/20 ${className}`}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Wallet className="w-5 h-5 text-primary flex-shrink-0" />
              <CardTitle className="text-lg font-semibold truncate group-hover:text-primary transition-colors">
                {name}
              </CardTitle>
            </div>
            {description && (
              <CardDescription className="text-sm text-muted-foreground line-clamp-2">
                {description}
              </CardDescription>
            )}
          </div>
          
          <div className="flex items-center gap-2 ml-2">
            <Badge 
              variant="outline" 
              className={`text-xs ${getRoleColor(user_role)} flex items-center gap-1`}
            >
              {getRoleIcon(user_role)}
              {user_role}
            </Badge>
            
            {(canEdit || isOwner) && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild data-dropdown-trigger>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {canEdit && onEdit && (
                    <DropdownMenuItem onClick={handleMenuAction(onEdit)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Cashbook
                    </DropdownMenuItem>
                  )}
                  {isOwner && onManageCollaborators && (
                    <DropdownMenuItem onClick={handleMenuAction(onManageCollaborators)}>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Manage Collaborators
                    </DropdownMenuItem>
                  )}
                  {isOwner && onEdit && onManageCollaborators && <DropdownMenuSeparator />}
                  {isOwner && onDelete && (
                    <DropdownMenuItem 
                      onClick={handleMenuAction(onDelete)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Cashbook
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Financial Summary */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <TrendingUp className="w-3 h-3 text-green-500" />
              Income
            </div>
            <div className="text-sm font-medium text-green-600">
              {formatCurrency(total_income, currency)}
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <TrendingDown className="w-3 h-3 text-red-500" />
              Expenses
            </div>
            <div className="text-sm font-medium text-red-600">
              {formatCurrency(total_expenses, currency)}
            </div>
          </div>
        </div>
        
        {/* Current Balance */}
        <div className="space-y-1 mb-4">
          <div className="text-xs text-muted-foreground">Current Balance</div>
          <div className={`text-lg font-bold ${isPositiveBalance ? 'text-green-600' : 'text-red-600'}`}>
            {formatCurrency(current_balance, currency)}
          </div>
        </div>
        
        {/* Footer Info */}
        <div className="flex items-center justify-between text-xs text-muted-foreground pt-3 border-t">
          <div className="flex items-center gap-1">
            <span>{transaction_count}</span>
            <span>transaction{transaction_count !== 1 ? 's' : ''}</span>
          </div>
          
          {owner && !isOwner && (
            <div className="flex items-center gap-1">
              <span>by</span>
              <span className="font-medium">{owner.full_name}</span>
            </div>
          )}
          
          <div className="text-xs font-medium text-primary">
            {currency}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
