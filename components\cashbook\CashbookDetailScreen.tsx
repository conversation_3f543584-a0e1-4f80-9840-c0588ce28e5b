"use client"

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  ArrowLeft,
  Plus, 
  Search, 
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  UserPlus,
  Download,
  Calendar,
  TrendingUp,
  TrendingDown,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Loader2
} from 'lucide-react';
import { useCashbook } from '@/contexts/CashbookContext';
import FinancialOverview from './FinancialOverview';
import TransactionItem from './TransactionItem';
import CategorySelector from './CategorySelector';
import QuickTransactionActions from './QuickTransactionActions';
import CashInOutActions from './CashInOutActions';

interface CashbookDetailScreenProps {
  cashbookId: string;
  onBack: () => void;
  onEditCashbook?: () => void;
  onDeleteCashbook?: () => void;
  onManageCollaborators?: () => void;
  onCreateTransaction?: () => void;
  onEditTransaction?: (transactionId: string) => void;
}

export default function CashbookDetailScreen({
  cashbookId,
  onBack,
  onEditCashbook,
  onDeleteCashbook,
  onManageCollaborators,
  onCreateTransaction,
  onEditTransaction,
}: CashbookDetailScreenProps) {
  const {
    currentCashbook,
    transactions,
    categories,
    loading,
    error,
    ui,
    financialSummary,
    getFilteredTransactions,
    setFilters,
    clearFilters,
    getUserRole,
    hasPermission,
    selectCashbook,
    loadTransactions,
    loadCategories,
    createTransaction,
  } = useCashbook();

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<'all' | 'income' | 'expense'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all-categories');
  const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');

  // Load cashbook data when component mounts or cashbookId changes
  useEffect(() => {
    if (cashbookId) {
      // Load the specific cashbook (this would be implemented in the context)
      loadTransactions(cashbookId);
      loadCategories();
    }
  }, [cashbookId, loadTransactions, loadCategories]);

  // Update filters when local state changes
  useEffect(() => {
    const filters: any = {};
    
    if (selectedType !== 'all') {
      filters.type = selectedType;
    }
    
    if (selectedCategory !== 'all-categories') {
      filters.category_id = selectedCategory;
    }
    
    if (searchTerm) {
      filters.search = searchTerm;
    }
    
    // Add date range filters
    if (dateRange !== 'all') {
      const today = new Date();
      const startDate = new Date();
      
      switch (dateRange) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          filters.date_from = startDate.toISOString().split('T')[0];
          filters.date_to = today.toISOString().split('T')[0];
          break;
        case 'week':
          startDate.setDate(today.getDate() - 7);
          filters.date_from = startDate.toISOString().split('T')[0];
          filters.date_to = today.toISOString().split('T')[0];
          break;
        case 'month':
          startDate.setMonth(today.getMonth() - 1);
          filters.date_from = startDate.toISOString().split('T')[0];
          filters.date_to = today.toISOString().split('T')[0];
          break;
      }
    }
    
    setFilters(filters);
  }, [selectedType, selectedCategory, searchTerm, dateRange, setFilters]);

  const filteredTransactions = getFilteredTransactions();
  const userRole = currentCashbook ? getUserRole(currentCashbook.id) : null;
  const canEdit = hasPermission('edit', cashbookId);
  const canManage = hasPermission('manage', cashbookId);



  // Sort transactions
  const sortedTransactions = React.useMemo(() => {
    const sorted = [...filteredTransactions].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return sorted;
  }, [filteredTransactions, sortBy, sortOrder]);

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedCategory('all-categories');
    setDateRange('all');
    clearFilters();
  };

  const handleTransactionPress = (transaction: any) => {
    if (onEditTransaction && canEdit) {
      onEditTransaction(transaction.id);
    }
  };

  const handleDeleteTransaction = (transactionId: string) => {
    // This will be implemented when we add the delete functionality
    console.log('Delete transaction:', transactionId);
  };

  const handleQuickTransaction = async (data: {
    amount: number;
    type: 'income' | 'expense';
    category_id: string;
    description: string;
    date?: string;
    payment_method?: string;
  }) => {
    if (!currentCashbook) return;

    try {
      await createTransaction({
        cashbook_id: currentCashbook.id,
        ...data,
        date: data.date || new Date().toISOString().split('T')[0], // Use provided date or today's date
      });
    } catch (error) {
      console.error('Failed to create quick transaction:', error);
      throw error;
    }
  };

  if (loading.transactions && !currentCashbook) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center py-12">
          <CardContent>
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Loading Cashbook</h3>
            <p className="text-muted-foreground">Please wait while we fetch your cashbook data...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!currentCashbook) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center py-12">
          <CardContent>
            <h3 className="text-lg font-semibold mb-2">Cashbook Not Found</h3>
            <p className="text-muted-foreground mb-4">The requested cashbook could not be found.</p>
            <Button onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <div className="flex items-center gap-3 mb-1">
              <h1 className="text-3xl font-bold">{currentCashbook.name}</h1>
              <Badge variant="outline" className="text-sm">
                {userRole}
              </Badge>
            </div>
            {currentCashbook.description && (
              <p className="text-muted-foreground">{currentCashbook.description}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Cash In/Out Actions - Moved here from below */}
          {canEdit && (
            <CashInOutActions
              cashbookId={cashbookId}
              currency={currentCashbook.currency}
              categories={categories}
              onCreateTransaction={handleQuickTransaction}
              disabled={loading.creating}
              className="flex-shrink-0"
            />
          )}

          {canManage && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEditCashbook && (
                  <DropdownMenuItem onClick={onEditCashbook}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Cashbook
                  </DropdownMenuItem>
                )}
                {onManageCollaborators && (
                  <DropdownMenuItem onClick={onManageCollaborators}>
                    <UserPlus className="w-4 h-4 mr-2" />
                    Manage Collaborators
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem>
                  <Download className="w-4 h-4 mr-2" />
                  Export Data
                </DropdownMenuItem>
                {onEditCashbook && onDeleteCashbook && <DropdownMenuSeparator />}
                {onDeleteCashbook && (
                  <DropdownMenuItem 
                    onClick={onDeleteCashbook}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Cashbook
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Financial Overview */}
      {financialSummary && (
        <div className="mb-8">
          <FinancialOverview
            summary={financialSummary}
            className="w-full"
          />
        </div>
      )}







      {/* Quick Actions */}
      {canEdit && (
        <div className="mb-6">
          <QuickTransactionActions
            cashbookId={cashbookId}
            currency={currentCashbook.currency}
            categories={categories}
            onCreateTransaction={handleQuickTransaction}
            disabled={loading.creating}
          />
        </div>
      )}

      {/* Filters and Search */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Transactions</CardTitle>
          <CardDescription>
            View and manage all transactions in this cashbook
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filters */}
            <div className="flex flex-wrap gap-2">
              <Select value={selectedType} onValueChange={(value: any) => setSelectedType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-categories">All Categories</SelectItem>
                  {categories
                    .filter(cat => selectedType === 'all' || cat.type === selectedType)
                    .map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={dateRange} onValueChange={(value: any) => setDateRange(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="amount">Amount</SelectItem>
                  <SelectItem value="type">Type</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="icon"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
              </Button>
              
              <Button variant="outline" onClick={resetFilters}>
                <Filter className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <div className="w-4 h-4 rounded-full bg-red-500" />
              <p className="font-medium">Error loading transactions</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error.message}</p>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading.transactions && (
        <Card className="text-center py-12">
          <CardContent>
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Loading Transactions</h3>
            <p className="text-muted-foreground">Please wait while we fetch your transactions...</p>
          </CardContent>
        </Card>
      )}

      {/* Transactions List */}
      {!loading.transactions && sortedTransactions.length > 0 && (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 gap-4'
            : 'space-y-2'
        }>
          {sortedTransactions.map((transaction) => (
            <TransactionItem
              key={transaction.id}
              transaction={transaction}
              currency={currentCashbook.currency}
              canEdit={canEdit}
              onEdit={() => handleTransactionPress(transaction)}
              onDelete={() => handleDeleteTransaction(transaction.id)}
              onPress={() => handleTransactionPress(transaction)}
              compact={viewMode === 'list'}
              showDate={true}
              showUser={true}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading.transactions && sortedTransactions.length === 0 && transactions.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <TrendingUp className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Transactions Yet</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Start tracking your finances by adding your first transaction to this cashbook.
            </p>

          </CardContent>
        </Card>
      )}

      {/* No Results State */}
      {!loading.transactions && sortedTransactions.length === 0 && transactions.length > 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Transactions Found</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              No transactions match your current search and filter criteria. Try adjusting your filters.
            </p>
            <Button variant="outline" onClick={resetFilters}>
              <Filter className="w-4 h-4 mr-2" />
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Summary Footer */}
      {!loading.transactions && sortedTransactions.length > 0 && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                Showing {sortedTransactions.length} of {transactions.length} transactions
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <span>
                    {sortedTransactions.filter(t => t.type === 'income').length} income
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingDown className="w-4 h-4 text-red-500" />
                  <span>
                    {sortedTransactions.filter(t => t.type === 'expense').length} expenses
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
