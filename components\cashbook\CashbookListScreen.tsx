"use client"

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Wallet,
  Plus,
  ArrowRight,
  Search,
  Filter,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Loader2
} from 'lucide-react';
import { useCashbook } from '@/contexts/CashbookContext';
import { useRouter } from 'next/navigation';
import CashbookCard from './CashbookCard';

// Supported currencies
const SUPPORTED_CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'NPR', name: 'Nepalese Rupee', symbol: 'Rs.' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

interface CreateCashbookFormData {
  name: string;
  description: string;
  currency: string;
}

export default function CashbookListScreen() {
  const router = useRouter();
  const {
    cashbooks,
    loading,
    error,
    createCashbook,
    selectCashbook,
    clearError,
  } = useCashbook();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState('all-currencies');
  const [selectedRole, setSelectedRole] = useState('all-roles');
  const [sortBy, setSortBy] = useState<'name' | 'created_at' | 'balance'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isCreating, setIsCreating] = useState(false);

  const [formData, setFormData] = useState<CreateCashbookFormData>({
    name: '',
    description: '',
    currency: 'USD',
  });

  const [formErrors, setFormErrors] = useState<Partial<CreateCashbookFormData>>({});

  // Clear error when component mounts
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [error, clearError]);

  // Filter and sort cashbooks
  const filteredAndSortedCashbooks = React.useMemo(() => {
    let filtered = cashbooks.filter(cashbook => {
      const matchesSearch = searchTerm === '' ||
        cashbook.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cashbook.description?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCurrency = selectedCurrency === 'all-currencies' || cashbook.currency === selectedCurrency;

      const matchesRole = selectedRole === 'all-roles' || cashbook.user_role === selectedRole;

      return matchesSearch && matchesCurrency && matchesRole;
    });

    // Sort cashbooks
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'created_at':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          break;
        case 'balance':
          const aBalance = (a.current_balance || 0);
          const bBalance = (b.current_balance || 0);
          comparison = aBalance - bBalance;
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [cashbooks, searchTerm, selectedCurrency, selectedRole, sortBy, sortOrder]);

  const validateForm = (): boolean => {
    const errors: Partial<CreateCashbookFormData> = {};

    if (!formData.name.trim()) {
      errors.name = 'Cashbook name is required';
    } else if (formData.name.length > 100) {
      errors.name = 'Name must be 100 characters or less';
    }

    if (formData.description && formData.description.length > 500) {
      errors.description = 'Description must be 500 characters or less';
    }

    if (!formData.currency) {
      errors.currency = 'Currency is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateCashbook = async () => {
    if (!validateForm()) return;

    setIsCreating(true);
    try {
      const newCashbook = await createCashbook({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        currency: formData.currency,
      });

      if (newCashbook) {
        setFormData({ name: '', description: '', currency: 'USD' });
        setFormErrors({});
        setIsCreateModalOpen(false);
      }
    } catch (error) {
      console.error('Failed to create cashbook:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleCashbookPress = (cashbook: any) => {
    selectCashbook(cashbook);
    router.push(`/apps/cashbook/${cashbook.id}`);
  };

  const handleInputChange = (field: keyof CreateCashbookFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedCurrency('all-currencies');
    setSelectedRole('all-roles');
    setSortBy('created_at');
    setSortOrder('desc');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Wallet className="w-8 h-8 text-primary" />
            CashBook Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage your financial records and track transactions across multiple cashbooks
          </p>
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create Cashbook
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Wallet className="w-5 h-5" />
                Create New Cashbook
              </DialogTitle>
              <DialogDescription>
                Create a new cashbook to start tracking your financial transactions.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cashbook-name">Name *</Label>
                <Input
                  id="cashbook-name"
                  placeholder="Enter cashbook name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={isCreating}
                  className={formErrors.name ? 'border-red-500' : ''}
                />
                {formErrors.name && (
                  <p className="text-sm text-red-500">{formErrors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="cashbook-description">Description</Label>
                <Textarea
                  id="cashbook-description"
                  placeholder="Enter description (optional)"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  disabled={isCreating}
                  rows={3}
                  className={formErrors.description ? 'border-red-500' : ''}
                />
                {formErrors.description && (
                  <p className="text-sm text-red-500">{formErrors.description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="cashbook-currency">Currency *</Label>
                <Select
                  value={formData.currency}
                  onValueChange={(value) => handleInputChange('currency', value)}
                  disabled={isCreating}
                >
                  <SelectTrigger className={formErrors.currency ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_CURRENCIES.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code}>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{currency.symbol}</span>
                          <span>{currency.name} ({currency.code})</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.currency && (
                  <p className="text-sm text-red-500">{formErrors.currency}</p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsCreateModalOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateCashbook}
                disabled={isCreating}
                className="flex items-center gap-2"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4" />
                    Create Cashbook
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Search */}
      {cashbooks.length > 0 && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search cashbooks..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap gap-2">
                <Select value={selectedCurrency} onValueChange={setSelectedCurrency}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="All Currencies" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-currencies">All Currencies</SelectItem>
                    {SUPPORTED_CURRENCIES.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code}>
                        {currency.code}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All Roles" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-roles">All Roles</SelectItem>
                    <SelectItem value="owner">Owner</SelectItem>
                    <SelectItem value="editor">Editor</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Date</SelectItem>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="balance">Balance</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                >
                  {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
                </Button>

                <Button variant="outline" onClick={resetFilters}>
                  <Filter className="w-4 h-4 mr-2" />
                  Reset
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading.cashbooks && (
        <Card className="text-center py-12">
          <CardContent>
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Loading Cashbooks</h3>
            <p className="text-muted-foreground">Please wait while we fetch your cashbooks...</p>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <div className="w-4 h-4 rounded-full bg-red-500" />
              <p className="font-medium">Error loading cashbooks</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error.message}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={clearError}
              className="mt-3"
            >
              Dismiss
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Cashbooks Grid/List */}
      {!loading.cashbooks && filteredAndSortedCashbooks.length > 0 && (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredAndSortedCashbooks.map((cashbook) => (
            <CashbookCard
              key={cashbook.id}
              cashbook={cashbook}
              onPress={() => handleCashbookPress(cashbook)}
              onEdit={() => console.log('Edit cashbook:', cashbook.id)}
              onDelete={() => console.log('Delete cashbook:', cashbook.id)}
              onManageCollaborators={() => console.log('Manage collaborators:', cashbook.id)}
              className={viewMode === 'list' ? 'w-full' : ''}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading.cashbooks && cashbooks.length === 0 && (
        <>
          {/* Welcome Card */}
          <Card className="mb-8 bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
            <CardHeader>
              <CardTitle className="text-xl">Welcome to CashBook Management</CardTitle>
              <CardDescription>
                Get started by creating your first cashbook to track income, expenses, and collaborate with your team.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Wallet className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Multi-Currency</h3>
                    <p className="text-sm text-muted-foreground">Support for multiple currencies</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <ArrowRight className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Real-time Sync</h3>
                    <p className="text-sm text-muted-foreground">Instant updates across devices</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Plus className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Collaboration</h3>
                    <p className="text-sm text-muted-foreground">Share with team members</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="text-center py-12">
            <CardContent>
              <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
                <Wallet className="w-12 h-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No Cashbooks Yet</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Create your first cashbook to start tracking your financial transactions and managing your money effectively.
              </p>
              <Button
                size="lg"
                className="flex items-center gap-2 mx-auto"
                onClick={() => setIsCreateModalOpen(true)}
              >
                <Plus className="w-4 h-4" />
                Create Your First Cashbook
              </Button>
            </CardContent>
          </Card>
        </>
      )}

      {/* No Results State */}
      {!loading.cashbooks && cashbooks.length > 0 && filteredAndSortedCashbooks.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Cashbooks Found</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              No cashbooks match your current search and filter criteria. Try adjusting your filters or search term.
            </p>
            <Button variant="outline" onClick={resetFilters}>
              <Filter className="w-4 h-4 mr-2" />
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
