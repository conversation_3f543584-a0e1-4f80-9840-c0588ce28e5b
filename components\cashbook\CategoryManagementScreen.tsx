"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Plus,
  Tag,
  TrendingUp,
  TrendingDown,
  Star,
  User,
  Search,
  Filter,
  Loader2,
  Edit,
  Trash2
} from 'lucide-react';
import { useCashbook } from '@/contexts/CashbookContext';

interface CategoryManagementScreenProps {
  onBack?: () => void;
}

export default function CategoryManagementScreen({ onBack }: CategoryManagementScreenProps) {
  const {
    categories,
    loading,
    error,
    createCategory,
    clearError,
  } = useCashbook();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all');
  const [filterSource, setFilterSource] = useState<'all' | 'default' | 'custom'>('all');
  const [isCreating, setIsCreating] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    type: 'expense' as 'income' | 'expense',
  });

  const [formErrors, setFormErrors] = useState<{ name?: string; type?: string }>({});

  // Filter categories
  const filteredCategories = React.useMemo(() => {
    return categories.filter(category => {
      const matchesSearch = searchTerm === '' || 
        category.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = filterType === 'all' || category.type === filterType;
      
      const matchesSource = filterSource === 'all' || 
        (filterSource === 'default' && category.is_default) ||
        (filterSource === 'custom' && !category.is_default);
      
      return matchesSearch && matchesType && matchesSource;
    });
  }, [categories, searchTerm, filterType, filterSource]);

  // Group categories
  const defaultCategories = filteredCategories.filter(cat => cat.is_default);
  const customCategories = filteredCategories.filter(cat => !cat.is_default);

  const validateForm = (): boolean => {
    const errors: { name?: string; type?: string } = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Category name is required';
    } else if (formData.name.length > 50) {
      errors.name = 'Category name must be 50 characters or less';
    } else {
      // Check for duplicate names within the same type
      const duplicate = categories.find(
        cat => cat.name.toLowerCase() === formData.name.toLowerCase() && cat.type === formData.type
      );
      if (duplicate) {
        errors.name = `A ${formData.type} category with this name already exists`;
      }
    }
    
    if (!formData.type) {
      errors.type = 'Category type is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateCategory = async () => {
    if (!validateForm()) return;

    setIsCreating(true);
    try {
      await createCategory({
        name: formData.name.trim(),
        type: formData.type,
      });

      setFormData({ name: '', type: 'expense' });
      setFormErrors({});
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error('Failed to create category:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleInputChange = (field: 'name' | 'type', value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const resetFilters = () => {
    setSearchTerm('');
    setFilterType('all');
    setFilterSource('all');
  };

  const getCategoryIcon = (category: any) => {
    if (category.is_default) {
      return <Star className="w-4 h-4 text-yellow-500" />;
    }
    return <User className="w-4 h-4 text-blue-500" />;
  };

  const getCategoryBadgeColor = (type: 'income' | 'expense'): string => {
    return type === 'income' 
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Tag className="w-8 h-8 text-primary" />
            Category Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage transaction categories for better organization
          </p>
        </div>
        
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create Category
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Create New Category
              </DialogTitle>
              <DialogDescription>
                Create a custom category for organizing transactions.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="category-name">Category Name *</Label>
                <Input
                  id="category-name"
                  placeholder="Enter category name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={isCreating}
                  className={formErrors.name ? 'border-red-500' : ''}
                />
                {formErrors.name && (
                  <p className="text-sm text-red-500">{formErrors.name}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category-type">Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: 'income' | 'expense') => handleInputChange('type', value)}
                  disabled={isCreating}
                >
                  <SelectTrigger className={formErrors.type ? 'border-red-500' : ''}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="income">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-600" />
                        Income
                      </div>
                    </SelectItem>
                    <SelectItem value="expense">
                      <div className="flex items-center gap-2">
                        <TrendingDown className="w-4 h-4 text-red-600" />
                        Expense
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {formErrors.type && (
                  <p className="text-sm text-red-500">{formErrors.type}</p>
                )}
              </div>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsCreateModalOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleCreateCategory}
                disabled={isCreating}
                className="flex items-center gap-2"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4" />
                    Create Category
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filters */}
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterSource} onValueChange={(value: any) => setFilterSource(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  <SelectItem value="default">Default</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
              
              <Button variant="outline" onClick={resetFilters}>
                <Filter className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <div className="w-4 h-4 rounded-full bg-red-500" />
              <p className="font-medium">Error loading categories</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error.message}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearError}
              className="mt-3"
            >
              Dismiss
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading.categories && (
        <Card className="text-center py-12">
          <CardContent>
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Loading Categories</h3>
            <p className="text-muted-foreground">Please wait while we fetch your categories...</p>
          </CardContent>
        </Card>
      )}

      {/* Categories List */}
      {!loading.categories && (
        <div className="space-y-6">
          {/* Default Categories */}
          {defaultCategories.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-yellow-500" />
                  Default Categories ({defaultCategories.length})
                </CardTitle>
                <CardDescription>
                  Built-in categories available to all users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {defaultCategories.map((category) => (
                    <div
                      key={category.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(category)}
                        <div>
                          <p className="font-medium">{category.name}</p>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getCategoryBadgeColor(category.type)}`}
                          >
                            {category.type}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Custom Categories */}
          {customCategories.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5 text-blue-500" />
                  Custom Categories ({customCategories.length})
                </CardTitle>
                <CardDescription>
                  Categories created by users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {customCategories.map((category) => (
                    <div
                      key={category.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(category)}
                        <div>
                          <p className="font-medium">{category.name}</p>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getCategoryBadgeColor(category.type)}`}
                          >
                            {category.type}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Empty State */}
          {filteredCategories.length === 0 && categories.length > 0 && (
            <Card className="text-center py-12">
              <CardContent>
                <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="w-12 h-12 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">No Categories Found</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  No categories match your current search and filter criteria.
                </p>
                <Button variant="outline" onClick={resetFilters}>
                  <Filter className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Summary */}
      {!loading.categories && categories.length > 0 && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold">{categories.length}</p>
                <p className="text-sm text-muted-foreground">Total Categories</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {categories.filter(c => c.type === 'income').length}
                </p>
                <p className="text-sm text-muted-foreground">Income Categories</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-red-600">
                  {categories.filter(c => c.type === 'expense').length}
                </p>
                <p className="text-sm text-muted-foreground">Expense Categories</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600">
                  {categories.filter(c => !c.is_default).length}
                </p>
                <p className="text-sm text-muted-foreground">Custom Categories</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
