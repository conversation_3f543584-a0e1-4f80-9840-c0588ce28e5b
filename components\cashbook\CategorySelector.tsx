"use client"

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Plus, 
  Tag, 
  Search,
  TrendingUp,
  TrendingDown,
  Star,
  User
} from 'lucide-react';

interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  is_default: boolean;
  created_by?: string;
}

interface CategorySelectorProps {
  categories: Category[];
  selectedCategoryId?: string;
  transactionType: 'income' | 'expense';
  onCategorySelect: (categoryId: string) => void;
  onCreateCategory?: (name: string, type: 'income' | 'expense') => Promise<void>;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

function getCategoryIcon(category: Category) {
  if (category.is_default) {
    return <Star className="w-3 h-3 text-yellow-500" />;
  }
  return <User className="w-3 h-3 text-blue-500" />;
}

function getCategoryBadgeColor(type: 'income' | 'expense'): string {
  return type === 'income' 
    ? 'bg-green-100 text-green-800 border-green-200'
    : 'bg-red-100 text-red-800 border-red-200';
}

export default function CategorySelector({
  categories,
  selectedCategoryId,
  transactionType,
  onCategorySelect,
  onCreateCategory,
  disabled = false,
  placeholder = "Select a category",
  className = '',
}: CategorySelectorProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter categories by transaction type and search term
  const filteredCategories = useMemo(() => {
    return categories
      .filter(category => category.type === transactionType)
      .filter(category => 
        searchTerm === '' || 
        category.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => {
        // Sort by: default categories first, then alphabetically
        if (a.is_default && !b.is_default) return -1;
        if (!a.is_default && b.is_default) return 1;
        return a.name.localeCompare(b.name);
      });
  }, [categories, transactionType, searchTerm]);

  // Group categories
  const defaultCategories = filteredCategories.filter(cat => cat.is_default);
  const customCategories = filteredCategories.filter(cat => !cat.is_default);

  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim() || !onCreateCategory) return;

    setIsCreating(true);
    try {
      await onCreateCategory(newCategoryName.trim(), transactionType);
      setNewCategoryName('');
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error('Failed to create category:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && newCategoryName.trim()) {
      handleCreateCategory();
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor="category-select" className="text-sm font-medium">
        Category
      </Label>
      
      <div className="flex gap-2">
        <Select
          value={selectedCategoryId || ''}
          onValueChange={onCategorySelect}
          disabled={disabled}
        >
          <SelectTrigger className="flex-1">
            <SelectValue placeholder={placeholder}>
              {selectedCategory && (
                <div className="flex items-center gap-2">
                  {getCategoryIcon(selectedCategory)}
                  <span>{selectedCategory.name}</span>
                  <Badge 
                    variant="outline" 
                    className={`text-xs ml-auto ${getCategoryBadgeColor(selectedCategory.type)}`}
                  >
                    {selectedCategory.type}
                  </Badge>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-h-80">
            {/* Search */}
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 h-8"
                />
              </div>
            </div>

            {/* Default Categories */}
            {defaultCategories.length > 0 && (
              <>
                <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground bg-muted/50 flex items-center gap-1">
                  <Star className="w-3 h-3" />
                  Default Categories
                </div>
                {defaultCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2 w-full">
                      {getCategoryIcon(category)}
                      <span className="flex-1">{category.name}</span>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getCategoryBadgeColor(category.type)}`}
                      >
                        {category.type === 'income' ? (
                          <TrendingUp className="w-3 h-3" />
                        ) : (
                          <TrendingDown className="w-3 h-3" />
                        )}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* Custom Categories */}
            {customCategories.length > 0 && (
              <>
                <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground bg-muted/50 flex items-center gap-1">
                  <User className="w-3 h-3" />
                  Custom Categories
                </div>
                {customCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center gap-2 w-full">
                      {getCategoryIcon(category)}
                      <span className="flex-1">{category.name}</span>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getCategoryBadgeColor(category.type)}`}
                      >
                        {category.type === 'income' ? (
                          <TrendingUp className="w-3 h-3" />
                        ) : (
                          <TrendingDown className="w-3 h-3" />
                        )}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </>
            )}

            {/* No results */}
            {filteredCategories.length === 0 && (
              <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                No categories found
                {searchTerm && (
                  <div className="mt-1">
                    Try adjusting your search term
                  </div>
                )}
              </div>
            )}
          </SelectContent>
        </Select>

        {/* Create Category Button */}
        {onCreateCategory && (
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                size="icon"
                disabled={disabled}
                className="flex-shrink-0"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Tag className="w-5 h-5" />
                  Create New Category
                </DialogTitle>
                <DialogDescription>
                  Create a custom category for {transactionType} transactions.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="category-name">Category Name</Label>
                  <Input
                    id="category-name"
                    placeholder="Enter category name"
                    value={newCategoryName}
                    onChange={(e) => setNewCategoryName(e.target.value)}
                    onKeyPress={handleKeyPress}
                    disabled={isCreating}
                    autoFocus
                  />
                </div>
                
                <div className="flex items-center gap-2">
                  <Label className="text-sm">Type:</Label>
                  <Badge 
                    variant="outline" 
                    className={`${getCategoryBadgeColor(transactionType)} flex items-center gap-1`}
                  >
                    {transactionType === 'income' ? (
                      <TrendingUp className="w-3 h-3" />
                    ) : (
                      <TrendingDown className="w-3 h-3" />
                    )}
                    {transactionType}
                  </Badge>
                </div>
              </div>
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsCreateModalOpen(false)}
                  disabled={isCreating}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateCategory}
                  disabled={!newCategoryName.trim() || isCreating}
                  className="flex items-center gap-2"
                >
                  {isCreating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4" />
                      Create Category
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Selected Category Info */}
      {selectedCategory && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {getCategoryIcon(selectedCategory)}
          <span>
            {selectedCategory.is_default ? 'Default' : 'Custom'} {selectedCategory.type} category
          </span>
        </div>
      )}
    </div>
  );
}
