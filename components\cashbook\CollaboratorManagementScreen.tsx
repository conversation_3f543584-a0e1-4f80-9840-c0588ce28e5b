"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  UserPlus,
  Users,
  Mail,
  MoreVertical,
  Edit,
  Trash2,
  Crown,
  Shield,
  Eye,
  ArrowLeft,
  Loader2,
  AlertCircle,
  Check,
  X
} from 'lucide-react';
import { useCashbook } from '@/contexts/CashbookContext';

interface CollaboratorManagementScreenProps {
  cashbookId: string;
  onBack: () => void;
}

export default function CollaboratorManagementScreen({ 
  cashbookId, 
  onBack 
}: CollaboratorManagementScreenProps) {
  const {
    currentCashbook,
    collaborators,
    loading,
    error,
    inviteCollaborator,
    updateCollaborator,
    removeCollaborator,
    getUserRole,
    clearError,
  } = useCashbook();

  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isInviting, setIsInviting] = useState(false);
  const [inviteData, setInviteData] = useState({
    email: '',
    role: 'viewer' as 'editor' | 'viewer',
  });
  const [inviteErrors, setInviteErrors] = useState<{ email?: string; role?: string }>({});

  const userRole = currentCashbook ? getUserRole(currentCashbook.id) : null;
  const isOwner = userRole === 'owner';

  const validateInviteForm = (): boolean => {
    const errors: { email?: string; role?: string } = {};
    
    if (!inviteData.email.trim()) {
      errors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(inviteData.email)) {
        errors.email = 'Please enter a valid email address';
      }
    }
    
    if (!inviteData.role) {
      errors.role = 'Role is required';
    }
    
    setInviteErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInviteCollaborator = async () => {
    if (!validateInviteForm() || !currentCashbook) return;

    setIsInviting(true);
    try {
      await inviteCollaborator({
        cashbook_id: currentCashbook.id,
        email: inviteData.email.trim(),
        role: inviteData.role,
      });

      setInviteData({ email: '', role: 'viewer' });
      setInviteErrors({});
      setIsInviteModalOpen(false);
    } catch (error) {
      console.error('Failed to invite collaborator:', error);
    } finally {
      setIsInviting(false);
    }
  };

  const handleUpdateRole = async (collaboratorId: string, newRole: 'editor' | 'viewer') => {
    try {
      await updateCollaborator(collaboratorId, { role: newRole });
    } catch (error) {
      console.error('Failed to update collaborator role:', error);
    }
  };

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    const confirmRemove = window.confirm(
      'Are you sure you want to remove this collaborator? They will lose access to this cashbook.'
    );
    
    if (!confirmRemove) return;

    try {
      await removeCollaborator(collaboratorId);
    } catch (error) {
      console.error('Failed to remove collaborator:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="w-4 h-4 text-yellow-600" />;
      case 'editor':
        return <Shield className="w-4 h-4 text-blue-600" />;
      case 'viewer':
        return <Eye className="w-4 h-4 text-gray-600" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  const getRoleBadgeColor = (role: string): string => {
    switch (role) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'editor':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'viewer':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!currentCashbook) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center py-12">
          <CardContent>
            <h3 className="text-lg font-semibold mb-2">Cashbook Not Found</h3>
            <p className="text-muted-foreground mb-4">Please select a cashbook first.</p>
            <Button onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Users className="w-8 h-8 text-primary" />
            Manage Collaborators
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage access and permissions for "{currentCashbook.name}"
          </p>
        </div>
        
        {isOwner && (
          <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <UserPlus className="w-4 h-4" />
                Invite Collaborator
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Invite Collaborator
                </DialogTitle>
                <DialogDescription>
                  Send an invitation to collaborate on this cashbook.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="invite-email">Email Address *</Label>
                  <Input
                    id="invite-email"
                    type="email"
                    placeholder="Enter email address"
                    value={inviteData.email}
                    onChange={(e) => setInviteData(prev => ({ ...prev, email: e.target.value }))}
                    disabled={isInviting}
                    className={inviteErrors.email ? 'border-red-500' : ''}
                  />
                  {inviteErrors.email && (
                    <p className="text-sm text-red-500">{inviteErrors.email}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="invite-role">Role *</Label>
                  <Select
                    value={inviteData.role}
                    onValueChange={(value: 'editor' | 'viewer') => 
                      setInviteData(prev => ({ ...prev, role: value }))
                    }
                    disabled={isInviting}
                  >
                    <SelectTrigger className={inviteErrors.role ? 'border-red-500' : ''}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="editor">
                        <div className="flex items-center gap-2">
                          <Shield className="w-4 h-4 text-blue-600" />
                          <div>
                            <p className="font-medium">Editor</p>
                            <p className="text-xs text-muted-foreground">Can add and edit transactions</p>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="viewer">
                        <div className="flex items-center gap-2">
                          <Eye className="w-4 h-4 text-gray-600" />
                          <div>
                            <p className="font-medium">Viewer</p>
                            <p className="text-xs text-muted-foreground">Can only view transactions</p>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {inviteErrors.role && (
                    <p className="text-sm text-red-500">{inviteErrors.role}</p>
                  )}
                </div>
              </div>
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsInviteModalOpen(false)}
                  disabled={isInviting}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleInviteCollaborator}
                  disabled={isInviting}
                  className="flex items-center gap-2"
                >
                  {isInviting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4" />
                      Send Invitation
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-4 h-4" />
              <p className="font-medium">Error managing collaborators</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error.message}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearError}
              className="mt-3"
            >
              Dismiss
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Owner Card */}
      {currentCashbook.owner && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="w-5 h-5 text-yellow-600" />
              Owner
            </CardTitle>
            <CardDescription>
              The owner has full control over this cashbook
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-4 border rounded-lg bg-yellow-50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-yellow-800">
                    {currentCashbook.owner.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="font-medium">{currentCashbook.owner.full_name}</p>
                  <p className="text-sm text-muted-foreground">{currentCashbook.owner.email}</p>
                </div>
              </div>
              <Badge variant="outline" className={getRoleBadgeColor('owner')}>
                <Crown className="w-3 h-3 mr-1" />
                Owner
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Collaborators */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-primary" />
            Collaborators ({collaborators.length})
          </CardTitle>
          <CardDescription>
            Users who have been granted access to this cashbook
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading.collaborators ? (
            <div className="text-center py-8">
              <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-primary" />
              <p className="text-sm text-muted-foreground">Loading collaborators...</p>
            </div>
          ) : collaborators.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Collaborators Yet</h3>
              <p className="text-muted-foreground mb-4">
                Invite team members to collaborate on this cashbook.
              </p>
              {isOwner && (
                <Button 
                  onClick={() => setIsInviteModalOpen(true)}
                  className="flex items-center gap-2"
                >
                  <UserPlus className="w-4 h-4" />
                  Invite Your First Collaborator
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {collaborators.map((collaborator) => (
                <div
                  key={collaborator.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary">
                        {collaborator.user?.full_name?.split(' ').map(n => n[0]).join('').toUpperCase() || '?'}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{collaborator.user?.full_name || 'Unknown User'}</p>
                      <p className="text-sm text-muted-foreground">{collaborator.user?.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getRoleBadgeColor(collaborator.role)}>
                      {getRoleIcon(collaborator.role)}
                      {collaborator.role}
                    </Badge>
                    
                    {isOwner && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem 
                            onClick={() => handleUpdateRole(collaborator.id, 'editor')}
                            disabled={collaborator.role === 'editor'}
                          >
                            <Shield className="w-4 h-4 mr-2" />
                            Make Editor
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleUpdateRole(collaborator.id, 'viewer')}
                            disabled={collaborator.role === 'viewer'}
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            Make Viewer
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleRemoveCollaborator(collaborator.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Remove Access
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Permissions Info */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-lg">Permission Levels</CardTitle>
          <CardDescription>
            Understanding different access levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <Crown className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="font-medium">Owner</p>
                <p className="text-sm text-muted-foreground">
                  Full control: manage collaborators, edit/delete cashbook, all transaction permissions
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <p className="font-medium">Editor</p>
                <p className="text-sm text-muted-foreground">
                  Can add, edit, and delete transactions; view financial reports
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Eye className="w-5 h-5 text-gray-600 mt-0.5" />
              <div>
                <p className="font-medium">Viewer</p>
                <p className="text-sm text-muted-foreground">
                  Read-only access: view transactions and financial reports only
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
