"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Wallet, 
  BarChart3,
  ArrowUpR<PERSON>,
  ArrowDownRight,
  DollarSign
} from 'lucide-react';

interface FinancialOverviewProps {
  summary: {
    total_income: number;
    total_expenses: number;
    current_balance: number;
    transaction_count: number;
    currency: string;
  };
  previousPeriod?: {
    total_income: number;
    total_expenses: number;
    current_balance: number;
  };
  className?: string;
}

// Currency symbols mapping
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  NPR: 'Rs.',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
};

function formatCurrency(amount: number, currency: string): string {
  const symbol = CURRENCY_SYMBOLS[currency] || currency;
  const isNegative = amount < 0;
  const absoluteAmount = Math.abs(amount);
  
  const formatted = absoluteAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  const result = `${symbol}${formatted}`;
  return isNegative ? `-${result}` : result;
}

function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

function formatPercentage(percentage: number): string {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(1)}%`;
}

interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color: string;
  description?: string;
}

function MetricCard({ title, value, icon, trend, color, description }: MetricCardProps) {
  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardDescription className="text-sm font-medium">{title}</CardDescription>
          <div className={`p-2 rounded-lg ${color}`}>
            {icon}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-1">
          <CardTitle className="text-2xl font-bold">{value}</CardTitle>
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
          {trend && (
            <div className="flex items-center gap-1">
              {trend.isPositive ? (
                <ArrowUpRight className="w-3 h-3 text-green-500" />
              ) : (
                <ArrowDownRight className="w-3 h-3 text-red-500" />
              )}
              <span className={`text-xs font-medium ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatPercentage(Math.abs(trend.value))}
              </span>
              <span className="text-xs text-muted-foreground">vs last period</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default function FinancialOverview({ 
  summary, 
  previousPeriod, 
  className = '' 
}: FinancialOverviewProps) {
  const {
    total_income,
    total_expenses,
    current_balance,
    transaction_count,
    currency,
  } = summary;

  const isPositiveBalance = current_balance >= 0;
  const savingsRate = total_income > 0 ? ((total_income - total_expenses) / total_income) * 100 : 0;

  // Calculate trends if previous period data is available
  const incomeTrend = previousPeriod ? {
    value: calculatePercentageChange(total_income, previousPeriod.total_income),
    isPositive: total_income >= previousPeriod.total_income,
  } : undefined;

  const expensesTrend = previousPeriod ? {
    value: calculatePercentageChange(total_expenses, previousPeriod.total_expenses),
    isPositive: total_expenses <= previousPeriod.total_expenses, // Lower expenses are positive
  } : undefined;

  const balanceTrend = previousPeriod ? {
    value: calculatePercentageChange(current_balance, previousPeriod.current_balance),
    isPositive: current_balance >= previousPeriod.current_balance,
  } : undefined;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Income"
          value={formatCurrency(total_income, currency)}
          icon={<TrendingUp className="w-5 h-5 text-white" />}
          color="bg-gradient-to-br from-green-500 to-emerald-600"
          trend={incomeTrend}
          description="Money received"
        />
        
        <MetricCard
          title="Total Expenses"
          value={formatCurrency(total_expenses, currency)}
          icon={<TrendingDown className="w-5 h-5 text-white" />}
          color="bg-gradient-to-br from-red-500 to-rose-600"
          trend={expensesTrend}
          description="Money spent"
        />
        
        <MetricCard
          title="Current Balance"
          value={formatCurrency(current_balance, currency)}
          icon={<Wallet className="w-5 h-5 text-white" />}
          color={`bg-gradient-to-br ${
            isPositiveBalance 
              ? 'from-blue-500 to-cyan-600' 
              : 'from-orange-500 to-red-600'
          }`}
          trend={balanceTrend}
          description={isPositiveBalance ? "Available funds" : "Deficit amount"}
        />
        
        <MetricCard
          title="Transactions"
          value={transaction_count.toString()}
          icon={<BarChart3 className="w-5 h-5 text-white" />}
          color="bg-gradient-to-br from-purple-500 to-violet-600"
          description="Total entries"
        />
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Savings Rate Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-primary" />
              Savings Rate
            </CardTitle>
            <CardDescription>
              Percentage of income saved this period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-3xl font-bold">
                {savingsRate.toFixed(1)}%
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    savingsRate >= 0 ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min(Math.abs(savingsRate), 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
              <Badge 
                variant={savingsRate >= 20 ? "default" : savingsRate >= 0 ? "secondary" : "destructive"}
                className="text-xs"
              >
                {savingsRate >= 20 ? "Excellent" : savingsRate >= 10 ? "Good" : savingsRate >= 0 ? "Fair" : "Deficit"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              Quick Stats
            </CardTitle>
            <CardDescription>
              Key financial ratios and metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Expense Ratio</span>
                <span className="text-sm font-medium">
                  {total_income > 0 ? ((total_expenses / total_income) * 100).toFixed(1) : 0}%
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Avg per Transaction</span>
                <span className="text-sm font-medium">
                  {transaction_count > 0 
                    ? formatCurrency((total_income + total_expenses) / transaction_count, currency)
                    : formatCurrency(0, currency)
                  }
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Net Flow</span>
                <span className={`text-sm font-medium ${
                  current_balance >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatCurrency(total_income - total_expenses, currency)}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Currency</span>
                <Badge variant="outline" className="text-xs">
                  {currency}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
