"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Wallet, 
  BarChart3,
  Calendar,
  Users,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

interface QuickSummaryProps {
  cashbook: {
    id: string;
    name: string;
    currency: string;
    user_role?: 'owner' | 'editor' | 'viewer';
    total_income?: number;
    total_expenses?: number;
    current_balance?: number;
    transaction_count?: number;
    created_at: string;
  };
  recentTransactions?: number;
  collaboratorCount?: number;
  className?: string;
}

// Currency symbols mapping
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  NPR: 'Rs.',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
};

function formatCurrency(amount: number, currency: string): string {
  const symbol = CURRENCY_SYMBOLS[currency] || currency;
  const isNegative = amount < 0;
  const absoluteAmount = Math.abs(amount);
  
  const formatted = absoluteAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  const result = `${symbol}${formatted}`;
  return isNegative ? `-${result}` : result;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

function getRoleColor(role: string): string {
  switch (role) {
    case 'owner':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'editor':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'viewer':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

export default function QuickSummary({
  cashbook,
  recentTransactions = 0,
  collaboratorCount = 0,
  className = '',
}: QuickSummaryProps) {
  const {
    name,
    currency,
    user_role = 'viewer',
    total_income = 0,
    total_expenses = 0,
    current_balance = 0,
    transaction_count = 0,
    created_at,
  } = cashbook;

  const isPositiveBalance = current_balance >= 0;
  const savingsRate = total_income > 0 ? ((total_income - total_expenses) / total_income) * 100 : 0;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <Wallet className="w-5 h-5 text-primary" />
                {name}
              </CardTitle>
              <CardDescription className="flex items-center gap-2 mt-1">
                <Calendar className="w-4 h-4" />
                Created {formatDate(created_at)}
              </CardDescription>
            </div>
            <Badge variant="outline" className={`${getRoleColor(user_role)}`}>
              {user_role}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Financial Summary Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Income</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(total_income, currency)}
                </p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="w-4 h-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(total_expenses, currency)}
                </p>
              </div>
              <div className="p-2 bg-red-100 rounded-lg">
                <TrendingDown className="w-4 h-4 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Current Balance</p>
                <p className={`text-2xl font-bold ${isPositiveBalance ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(current_balance, currency)}
                </p>
              </div>
              <div className={`p-2 rounded-lg ${isPositiveBalance ? 'bg-blue-100' : 'bg-orange-100'}`}>
                <Wallet className={`w-4 h-4 ${isPositiveBalance ? 'text-blue-600' : 'text-orange-600'}`} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Transactions</p>
                <p className="text-2xl font-bold">
                  {transaction_count}
                </p>
              </div>
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart3 className="w-4 h-4 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Savings Rate</CardTitle>
            <CardDescription>
              Percentage of income saved this period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-3xl font-bold">
                  {savingsRate.toFixed(1)}%
                </span>
                <div className="flex items-center gap-1">
                  {savingsRate >= 0 ? (
                    <ArrowUpRight className="w-4 h-4 text-green-500" />
                  ) : (
                    <ArrowDownRight className="w-4 h-4 text-red-500" />
                  )}
                  <Badge 
                    variant={savingsRate >= 20 ? "default" : savingsRate >= 0 ? "secondary" : "destructive"}
                    className="text-xs"
                  >
                    {savingsRate >= 20 ? "Excellent" : savingsRate >= 10 ? "Good" : savingsRate >= 0 ? "Fair" : "Deficit"}
                  </Badge>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    savingsRate >= 0 ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min(Math.abs(savingsRate), 100)}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Activity Overview</CardTitle>
            <CardDescription>
              Recent activity and collaboration stats
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Recent Transactions</span>
                <span className="text-sm font-medium">{recentTransactions}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Collaborators</span>
                <div className="flex items-center gap-1">
                  <Users className="w-3 h-3 text-muted-foreground" />
                  <span className="text-sm font-medium">{collaboratorCount}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Avg per Transaction</span>
                <span className="text-sm font-medium">
                  {transaction_count > 0 
                    ? formatCurrency((total_income + total_expenses) / transaction_count, currency)
                    : formatCurrency(0, currency)
                  }
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Currency</span>
                <Badge variant="outline" className="text-xs">
                  {currency}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
