"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Plus,
  TrendingUp,
  TrendingDown,
  Zap,
  Coffee,
  Car,
  Home,
  ShoppingCart,
  Utensils,
  Loader2
} from 'lucide-react';

interface QuickTransactionActionsProps {
  cashbookId: string;
  currency: string;
  onCreateTransaction: (data: {
    amount: number;
    type: 'income' | 'expense';
    category_id: string;
    description: string;
  }) => Promise<void>;
  categories: Array<{
    id: string;
    name: string;
    type: 'income' | 'expense';
    is_default: boolean;
  }>;
  disabled?: boolean;
  className?: string;
}

interface QuickAction {
  id: string;
  name: string;
  type: 'income' | 'expense';
  icon: React.ReactNode;
  color: string;
  defaultAmount?: number;
  categoryName: string;
}

const QUICK_ACTIONS: QuickAction[] = [
  {
    id: 'coffee',
    name: 'Coffee',
    type: 'expense',
    icon: <Coffee className="w-4 h-4" />,
    color: 'bg-amber-100 text-amber-800',
    defaultAmount: 5,
    categoryName: 'Food & Dining',
  },
  {
    id: 'lunch',
    name: 'Lunch',
    type: 'expense',
    icon: <Utensils className="w-4 h-4" />,
    color: 'bg-orange-100 text-orange-800',
    defaultAmount: 15,
    categoryName: 'Food & Dining',
  },
  {
    id: 'gas',
    name: 'Gas',
    type: 'expense',
    icon: <Car className="w-4 h-4" />,
    color: 'bg-blue-100 text-blue-800',
    defaultAmount: 50,
    categoryName: 'Transportation',
  },
  {
    id: 'groceries',
    name: 'Groceries',
    type: 'expense',
    icon: <ShoppingCart className="w-4 h-4" />,
    color: 'bg-green-100 text-green-800',
    defaultAmount: 75,
    categoryName: 'Groceries',
  },
  {
    id: 'salary',
    name: 'Salary',
    type: 'income',
    icon: <TrendingUp className="w-4 h-4" />,
    color: 'bg-emerald-100 text-emerald-800',
    defaultAmount: 3000,
    categoryName: 'Salary',
  },
  {
    id: 'freelance',
    name: 'Freelance',
    type: 'income',
    icon: <Zap className="w-4 h-4" />,
    color: 'bg-purple-100 text-purple-800',
    defaultAmount: 500,
    categoryName: 'Freelance',
  },
];

export default function QuickTransactionActions({
  cashbookId,
  currency,
  onCreateTransaction,
  categories,
  disabled = false,
  className = '',
}: QuickTransactionActionsProps) {
  const [selectedAction, setSelectedAction] = useState<QuickAction | null>(null);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleQuickAction = (action: QuickAction) => {
    setSelectedAction(action);
    setAmount(action.defaultAmount?.toString() || '');
    setDescription(action.name);
    setIsModalOpen(true);
  };

  const handleSubmit = async () => {
    if (!selectedAction || !amount) return;

    const category = categories.find(
      cat => cat.name === selectedAction.categoryName && cat.type === selectedAction.type
    );

    if (!category) {
      console.error('Category not found:', selectedAction.categoryName);
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onCreateTransaction({
        amount: parseFloat(amount),
        type: selectedAction.type,
        category_id: category.id,
        description: description || selectedAction.name,
      });

      // Reset form and close modal
      setSelectedAction(null);
      setAmount('');
      setDescription('');
      setIsModalOpen(false);
    } catch (error) {
      console.error('Failed to create quick transaction:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setSelectedAction(null);
    setAmount('');
    setDescription('');
    setIsModalOpen(false);
  };

  // Filter actions based on available categories
  const availableActions = QUICK_ACTIONS.filter(action => 
    categories.some(cat => cat.name === action.categoryName && cat.type === action.type)
  );

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Zap className="w-5 h-5 text-primary" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Add common transactions with one click
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {availableActions.map((action) => (
              <Button
                key={action.id}
                variant="outline"
                onClick={() => handleQuickAction(action)}
                disabled={disabled}
                className="h-auto p-4 flex flex-col items-center gap-2 hover:scale-105 transition-transform"
              >
                <div className={`p-2 rounded-lg ${action.color}`}>
                  {action.icon}
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium">{action.name}</p>
                  {action.defaultAmount && (
                    <p className="text-xs text-muted-foreground">
                      {currency} {action.defaultAmount}
                    </p>
                  )}
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Transaction Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedAction?.icon}
              Quick {selectedAction?.name}
            </DialogTitle>
            <DialogDescription>
              Add a {selectedAction?.type} transaction for {selectedAction?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="quick-amount">Amount</Label>
              <div className="relative">
                <span className="absolute left-3 top-3 text-muted-foreground text-sm">
                  {currency}
                </span>
                <Input
                  id="quick-amount"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  disabled={isSubmitting}
                  className="pl-16"
                  autoFocus
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quick-description">Description</Label>
              <Input
                id="quick-description"
                placeholder="Transaction description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={isSubmitting}
              />
            </div>

            {selectedAction && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium capitalize">{selectedAction.type}</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-1">
                  <span className="text-muted-foreground">Category:</span>
                  <span className="font-medium">{selectedAction.categoryName}</span>
                </div>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={!amount || isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  Add Transaction
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
