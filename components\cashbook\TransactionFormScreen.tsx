"use client"

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft,
  Save,
  Loader2,
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  FileText,
  Tag,
  AlertCircle
} from 'lucide-react';
import { useCashbook } from '@/contexts/CashbookContext';
import CategorySelector from './CategorySelector';
import { CashbookValidation } from '@/lib/cashbook-validation';
import { TransactionHelpers } from '@/lib/transaction-helpers';

interface TransactionFormData {
  amount: string;
  type: 'income' | 'expense';
  category_id: string;
  description: string;
  date: string;
}

interface TransactionFormScreenProps {
  cashbookId: string;
  transactionId?: string; // For editing existing transactions
  onBack: () => void;
  onSave?: (transaction: any) => void;
  onCancel?: () => void;
}

export default function TransactionFormScreen({
  cashbookId,
  transactionId,
  onBack,
  onSave,
  onCancel,
}: TransactionFormScreenProps) {
  const {
    currentCashbook,
    transactions,
    categories,
    loading,
    error,
    createTransaction,
    updateTransaction,
    createCategory,
    clearError,
  } = useCashbook();

  const [formData, setFormData] = useState<TransactionFormData>({
    amount: '',
    type: 'expense',
    category_id: '',
    description: '',
    date: new Date().toISOString().split('T')[0], // Today's date
  });

  const [formErrors, setFormErrors] = useState<Partial<TransactionFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Check if we're editing an existing transaction
  const isEditing = !!transactionId;
  const existingTransaction = isEditing 
    ? transactions.find(t => t.id === transactionId)
    : null;

  // Load existing transaction data for editing
  useEffect(() => {
    if (isEditing && existingTransaction) {
      setFormData({
        amount: existingTransaction.amount.toString(),
        type: existingTransaction.type,
        category_id: existingTransaction.category_id,
        description: existingTransaction.description || '',
        date: existingTransaction.date,
      });
    }
  }, [isEditing, existingTransaction]);

  // Clear error when component mounts
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [error, clearError]);

  // Track unsaved changes
  useEffect(() => {
    if (isEditing && existingTransaction) {
      const hasChanges = 
        formData.amount !== existingTransaction.amount.toString() ||
        formData.type !== existingTransaction.type ||
        formData.category_id !== existingTransaction.category_id ||
        formData.description !== (existingTransaction.description || '') ||
        formData.date !== existingTransaction.date;
      
      setHasUnsavedChanges(hasChanges);
    } else {
      // For new transactions, check if any field has been filled
      const hasData = 
        formData.amount !== '' ||
        formData.description !== '' ||
        formData.category_id !== '';
      
      setHasUnsavedChanges(hasData);
    }
  }, [formData, isEditing, existingTransaction]);

  const validateForm = (): boolean => {
    const transactionData = {
      amount: parseFloat(formData.amount),
      type: formData.type,
      category_id: formData.category_id,
      description: formData.description,
      date: formData.date,
    };

    const validation = TransactionHelpers.validateTransactionData(transactionData, categories);

    // Add required field validation
    const errors = { ...validation.errors };

    if (!formData.amount.trim()) {
      errors.amount = 'Amount is required';
    }

    if (!formData.type) {
      errors.type = 'Transaction type is required';
    }

    if (!formData.category_id) {
      errors.category_id = 'Category is required';
    }

    if (!formData.date) {
      errors.date = 'Date is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof TransactionFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleTypeChange = (type: 'income' | 'expense') => {
    setFormData(prev => ({ 
      ...prev, 
      type,
      // Clear category when type changes to avoid invalid combinations
      category_id: ''
    }));
    
    // Clear type and category errors
    setFormErrors(prev => ({ 
      ...prev, 
      type: undefined,
      category_id: undefined
    }));
  };

  const handleCategorySelect = (categoryId: string) => {
    handleInputChange('category_id', categoryId);
  };

  const handleCreateCategory = async (name: string, type: 'income' | 'expense') => {
    try {
      const newCategory = await createCategory({ name, type });
      if (newCategory) {
        // Auto-select the newly created category
        setFormData(prev => ({ ...prev, category_id: newCategory.id }));
      }
    } catch (error) {
      console.error('Failed to create category:', error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (!currentCashbook) {
      console.error('No current cashbook selected');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const transactionData = {
        cashbook_id: currentCashbook.id,
        amount: parseFloat(formData.amount),
        type: formData.type,
        category_id: formData.category_id,
        description: formData.description.trim() || undefined,
        date: formData.date,
      };

      let result;
      if (isEditing && transactionId) {
        result = await updateTransaction(transactionId, transactionData);
      } else {
        result = await createTransaction(transactionData);
      }

      if (result) {
        setHasUnsavedChanges(false);
        if (onSave) {
          onSave(result);
        } else {
          onBack();
        }
      }
    } catch (error) {
      console.error('Failed to save transaction:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      const confirmDiscard = window.confirm(
        'You have unsaved changes. Are you sure you want to discard them?'
      );
      if (!confirmDiscard) {
        return;
      }
    }
    
    if (onCancel) {
      onCancel();
    } else {
      onBack();
    }
  };

  if (!currentCashbook) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center py-12">
          <CardContent>
            <h3 className="text-lg font-semibold mb-2">Cashbook Not Found</h3>
            <p className="text-muted-foreground mb-4">Please select a cashbook first.</p>
            <Button onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">
            {isEditing ? 'Edit Transaction' : 'Add Transaction'}
          </h1>
          <p className="text-muted-foreground">
            {isEditing ? 'Update transaction details' : 'Add a new transaction to'} {currentCashbook.name}
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="w-4 h-4" />
              <p className="font-medium">Error saving transaction</p>
            </div>
            <p className="text-sm text-red-600 mt-1">{error.message}</p>
          </CardContent>
        </Card>
      )}

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {formData.type === 'income' ? (
              <TrendingUp className="w-5 h-5 text-green-600" />
            ) : (
              <TrendingDown className="w-5 h-5 text-red-600" />
            )}
            Transaction Details
          </CardTitle>
          <CardDescription>
            Fill in the transaction information below
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Transaction Type */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Transaction Type *</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant={formData.type === 'income' ? 'default' : 'outline'}
                  onClick={() => handleTypeChange('income')}
                  className="flex-1 flex items-center gap-2"
                  disabled={isSubmitting}
                >
                  <TrendingUp className="w-4 h-4" />
                  Income
                </Button>
                <Button
                  type="button"
                  variant={formData.type === 'expense' ? 'default' : 'outline'}
                  onClick={() => handleTypeChange('expense')}
                  className="flex-1 flex items-center gap-2"
                  disabled={isSubmitting}
                >
                  <TrendingDown className="w-4 h-4" />
                  Expense
                </Button>
              </div>
              {formErrors.type && (
                <p className="text-sm text-red-500">{formErrors.type}</p>
              )}
            </div>

            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="w-4 h-4" />
                Amount *
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-3 text-muted-foreground">
                  {currentCashbook.currency}
                </span>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0"
                  max="999999999.99"
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  disabled={isSubmitting}
                  className={`pl-16 ${formErrors.amount ? 'border-red-500' : ''}`}
                />
              </div>
              {formErrors.amount && (
                <p className="text-sm text-red-500">{formErrors.amount}</p>
              )}
            </div>

            {/* Category */}
            <CategorySelector
              categories={categories}
              selectedCategoryId={formData.category_id}
              transactionType={formData.type}
              onCategorySelect={handleCategorySelect}
              onCreateCategory={handleCreateCategory}
              disabled={isSubmitting}
              placeholder="Select a category"
            />
            {formErrors.category_id && (
              <p className="text-sm text-red-500">{formErrors.category_id}</p>
            )}

            {/* Date */}
            <div className="space-y-2">
              <Label htmlFor="date" className="text-sm font-medium flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Date *
              </Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                disabled={isSubmitting}
                className={formErrors.date ? 'border-red-500' : ''}
              />
              {formErrors.date && (
                <p className="text-sm text-red-500">{formErrors.date}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Description
              </Label>
              <Textarea
                id="description"
                placeholder="Enter transaction description (optional)"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                disabled={isSubmitting}
                rows={3}
                maxLength={500}
                className={formErrors.description ? 'border-red-500' : ''}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{formErrors.description || ''}</span>
                <span>{formData.description.length}/500</span>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex gap-3 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    {isEditing ? 'Update Transaction' : 'Create Transaction'}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Unsaved Changes Warning */}
      {hasUnsavedChanges && (
        <Card className="mt-6 border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertCircle className="w-4 h-4" />
              <p className="text-sm font-medium">You have unsaved changes</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
