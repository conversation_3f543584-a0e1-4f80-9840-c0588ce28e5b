"use client"

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  MoreVertical,
  Edit,
  Trash2,
  Calendar,
  Tag,
  User
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TransactionItemProps {
  transaction: {
    id: string;
    amount: number;
    type: 'income' | 'expense';
    description?: string;
    date: string;
    category?: {
      id: string;
      name: string;
      type: 'income' | 'expense';
    };
    created_by_user?: {
      id: string;
      full_name: string;
      email: string;
    };
  };
  currency: string;
  canEdit?: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
  onPress?: () => void;
  className?: string;
  showDate?: boolean;
  showUser?: boolean;
  compact?: boolean;
}

// Currency symbols mapping
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  NPR: 'Rs.',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
};

function formatCurrency(amount: number, currency: string): string {
  const symbol = CURRENCY_SYMBOLS[currency] || currency;
  const formatted = amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return `${symbol}${formatted}`;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  // Check if it's today
  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  }
  
  // Check if it's yesterday
  if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  }
  
  // Check if it's within the current week
  const weekAgo = new Date(today);
  weekAgo.setDate(weekAgo.getDate() - 7);
  if (date > weekAgo) {
    return date.toLocaleDateString('en-US', { weekday: 'long' });
  }
  
  // Otherwise show the date
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
  });
}

function getCategoryColor(type: 'income' | 'expense'): string {
  return type === 'income' 
    ? 'bg-green-100 text-green-800 border-green-200'
    : 'bg-red-100 text-red-800 border-red-200';
}

export default function TransactionItem({
  transaction,
  currency,
  canEdit = false,
  onEdit,
  onDelete,
  onPress,
  className = '',
  showDate = true,
  showUser = false,
  compact = false,
}: TransactionItemProps) {
  const {
    amount,
    type,
    description,
    date,
    category,
    created_by_user,
  } = transaction;

  const isIncome = type === 'income';
  const amountColor = isIncome ? 'text-green-600' : 'text-red-600';
  const icon = isIncome ? TrendingUp : TrendingDown;
  const IconComponent = icon;

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on dropdown menu
    if ((e.target as HTMLElement).closest('[data-dropdown-trigger]')) {
      return;
    }
    if (onPress) {
      onPress();
    }
  };

  const handleMenuAction = (action: () => void) => {
    return (e: React.MouseEvent) => {
      e.stopPropagation();
      action();
    };
  };

  if (compact) {
    return (
      <div 
        className={`flex items-center justify-between p-3 border-b hover:bg-muted/50 transition-colors ${
          onPress ? 'cursor-pointer' : ''
        } ${className}`}
        onClick={handleCardClick}
      >
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className={`p-2 rounded-lg ${isIncome ? 'bg-green-100' : 'bg-red-100'}`}>
            <IconComponent className={`w-4 h-4 ${isIncome ? 'text-green-600' : 'text-red-600'}`} />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <p className="text-sm font-medium truncate">
                {description || 'No description'}
              </p>
              {category && (
                <Badge variant="outline" className={`text-xs ${getCategoryColor(category.type)}`}>
                  {category.name}
                </Badge>
              )}
            </div>
            {showDate && (
              <p className="text-xs text-muted-foreground">
                {formatDate(date)}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <div className={`text-sm font-semibold ${amountColor}`}>
            {isIncome ? '+' : '-'}{formatCurrency(amount, currency)}
          </div>
          
          {canEdit && (onEdit || onDelete) && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild data-dropdown-trigger>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={handleMenuAction(onEdit)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onEdit && onDelete && <DropdownMenuSeparator />}
                {onDelete && (
                  <DropdownMenuItem 
                    onClick={handleMenuAction(onDelete)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card 
      className={`group transition-all duration-200 hover:shadow-md ${
        onPress ? 'cursor-pointer hover:scale-[1.01]' : ''
      } ${className}`}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            <div className={`p-2 rounded-lg ${isIncome ? 'bg-green-100' : 'bg-red-100'} flex-shrink-0`}>
              <IconComponent className={`w-5 h-5 ${isIncome ? 'text-green-600' : 'text-red-600'}`} />
            </div>
            
            <div className="flex-1 min-w-0 space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium truncate">
                    {description || 'No description'}
                  </h4>
                  
                  <div className="flex items-center gap-2 mt-1">
                    {category && (
                      <div className="flex items-center gap-1">
                        <Tag className="w-3 h-3 text-muted-foreground" />
                        <Badge variant="outline" className={`text-xs ${getCategoryColor(category.type)}`}>
                          {category.name}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-2">
                  <div className={`text-lg font-bold ${amountColor}`}>
                    {isIncome ? '+' : '-'}{formatCurrency(amount, currency)}
                  </div>
                  
                  {canEdit && (onEdit || onDelete) && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild data-dropdown-trigger>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEdit && (
                          <DropdownMenuItem onClick={handleMenuAction(onEdit)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Transaction
                          </DropdownMenuItem>
                        )}
                        {onEdit && onDelete && <DropdownMenuSeparator />}
                        {onDelete && (
                          <DropdownMenuItem 
                            onClick={handleMenuAction(onDelete)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Transaction
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center gap-4">
                  {showDate && (
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(date)}</span>
                    </div>
                  )}
                  
                  {showUser && created_by_user && (
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>{created_by_user.full_name}</span>
                    </div>
                  )}
                </div>
                
                <Badge 
                  variant="outline" 
                  className={`text-xs ${isIncome ? 'border-green-200 text-green-700' : 'border-red-200 text-red-700'}`}
                >
                  {type}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
