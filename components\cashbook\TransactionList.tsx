"use client"

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  Filter,
  Grid,
  List,
  SortAsc,
  SortDesc,
  TrendingUp,
  TrendingDown,
  Plus,
  Loader2
} from 'lucide-react';
import TransactionItem from './TransactionItem';

interface Transaction {
  id: string;
  amount: number;
  type: 'income' | 'expense';
  description?: string;
  date: string;
  category?: {
    id: string;
    name: string;
    type: 'income' | 'expense';
  };
  created_by_user?: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  is_default: boolean;
}

interface TransactionListProps {
  transactions: Transaction[];
  categories: Category[];
  currency: string;
  loading?: boolean;
  canEdit?: boolean;
  onCreateTransaction?: () => void;
  onEditTransaction?: (transactionId: string) => void;
  onDeleteTransaction?: (transactionId: string) => void;
  onTransactionPress?: (transaction: Transaction) => void;
  showFilters?: boolean;
  showHeader?: boolean;
  compact?: boolean;
  className?: string;
}

export default function TransactionList({
  transactions,
  categories,
  currency,
  loading = false,
  canEdit = false,
  onCreateTransaction,
  onEditTransaction,
  onDeleteTransaction,
  onTransactionPress,
  showFilters = true,
  showHeader = true,
  compact = false,
  className = '',
}: TransactionListProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<'all' | 'income' | 'expense'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all-categories');
  const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');

  // Filter transactions
  const filteredTransactions = React.useMemo(() => {
    return transactions.filter(transaction => {
      // Type filter
      if (selectedType !== 'all' && transaction.type !== selectedType) {
        return false;
      }
      
      // Category filter
      if (selectedCategory !== 'all-categories' && transaction.category?.id !== selectedCategory) {
        return false;
      }
      
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const description = transaction.description?.toLowerCase() || '';
        const categoryName = transaction.category?.name?.toLowerCase() || '';
        
        if (!description.includes(searchLower) && !categoryName.includes(searchLower)) {
          return false;
        }
      }
      
      // Date range filter
      if (dateRange !== 'all') {
        const transactionDate = new Date(transaction.date);
        const today = new Date();
        const startDate = new Date();
        
        switch (dateRange) {
          case 'today':
            startDate.setHours(0, 0, 0, 0);
            if (transactionDate < startDate || transactionDate > today) {
              return false;
            }
            break;
          case 'week':
            startDate.setDate(today.getDate() - 7);
            if (transactionDate < startDate) {
              return false;
            }
            break;
          case 'month':
            startDate.setMonth(today.getMonth() - 1);
            if (transactionDate < startDate) {
              return false;
            }
            break;
        }
      }
      
      return true;
    });
  }, [transactions, selectedType, selectedCategory, searchTerm, dateRange]);

  // Sort transactions
  const sortedTransactions = React.useMemo(() => {
    return [...filteredTransactions].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [filteredTransactions, sortBy, sortOrder]);

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedCategory('all-categories');
    setDateRange('all');
  };

  const handleTransactionPress = (transaction: Transaction) => {
    if (onTransactionPress) {
      onTransactionPress(transaction);
    } else if (onEditTransaction && canEdit) {
      onEditTransaction(transaction.id);
    }
  };

  const handleEditTransaction = (transactionId: string) => {
    if (onEditTransaction) {
      onEditTransaction(transactionId);
    }
  };

  const handleDeleteTransaction = (transactionId: string) => {
    if (onDeleteTransaction) {
      onDeleteTransaction(transactionId);
    }
  };

  return (
    <div className={className}>
      {showHeader && (
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Transactions</CardTitle>
                <CardDescription>
                  {transactions.length} transaction{transactions.length !== 1 ? 's' : ''} total
                </CardDescription>
              </div>
              {canEdit && onCreateTransaction && (
                <>
                  {/* Mobile: Show Add Transaction button */}
                  <Button
                    onClick={onCreateTransaction}
                    className="flex md:hidden items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add Transaction
                  </Button>

                  {/* Desktop: Show Cash In/Out buttons */}
                  <div className="hidden md:flex items-center gap-2">
                    <Button
                      onClick={onCreateTransaction}
                      className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
                    >
                      <TrendingUp className="w-4 h-4" />
                      Cash In
                    </Button>
                    <Button
                      onClick={onCreateTransaction}
                      variant="destructive"
                      className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
                    >
                      <TrendingDown className="w-4 h-4" />
                      Cash Out
                    </Button>
                  </div>
                </>
              )}
            </div>
          </CardHeader>
          
          {showFilters && (
            <CardContent>
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search transactions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                {/* Filters */}
                <div className="flex flex-wrap gap-2">
                  <Select value={selectedType} onValueChange={(value: any) => setSelectedType(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="income">Income</SelectItem>
                      <SelectItem value="expense">Expense</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-categories">All Categories</SelectItem>
                      {categories
                        .filter(cat => selectedType === 'all' || cat.type === selectedType)
                        .map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Select value={dateRange} onValueChange={(value: any) => setDateRange(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="amount">Amount</SelectItem>
                      <SelectItem value="type">Type</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  >
                    {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  >
                    {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
                  </Button>
                  
                  <Button variant="outline" onClick={resetFilters}>
                    <Filter className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card className="text-center py-12">
          <CardContent>
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Loading Transactions</h3>
            <p className="text-muted-foreground">Please wait while we fetch your transactions...</p>
          </CardContent>
        </Card>
      )}

      {/* Transactions List */}
      {!loading && sortedTransactions.length > 0 && (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 gap-4'
            : 'space-y-2'
        }>
          {sortedTransactions.map((transaction) => (
            <TransactionItem
              key={transaction.id}
              transaction={transaction}
              currency={currency}
              canEdit={canEdit}
              onEdit={() => handleEditTransaction(transaction.id)}
              onDelete={() => handleDeleteTransaction(transaction.id)}
              onPress={() => handleTransactionPress(transaction)}
              compact={compact || viewMode === 'list'}
              showDate={true}
              showUser={true}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!loading && sortedTransactions.length === 0 && transactions.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <TrendingUp className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Transactions Yet</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Start tracking your finances by adding your first transaction.
            </p>

          </CardContent>
        </Card>
      )}

      {/* No Results State */}
      {!loading && sortedTransactions.length === 0 && transactions.length > 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Transactions Found</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              No transactions match your current search and filter criteria. Try adjusting your filters.
            </p>
            <Button variant="outline" onClick={resetFilters}>
              <Filter className="w-4 h-4 mr-2" />
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Summary Footer */}
      {!loading && sortedTransactions.length > 0 && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                Showing {sortedTransactions.length} of {transactions.length} transactions
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <span>
                    {sortedTransactions.filter(t => t.type === 'income').length} income
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingDown className="w-4 h-4 text-red-500" />
                  <span>
                    {sortedTransactions.filter(t => t.type === 'expense').length} expenses
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
