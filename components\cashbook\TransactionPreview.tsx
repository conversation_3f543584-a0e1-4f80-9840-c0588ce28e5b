"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  Tag,
  FileText,
  DollarSign,
  User
} from 'lucide-react';

interface TransactionPreviewProps {
  transaction: {
    amount: number;
    type: 'income' | 'expense';
    description?: string;
    date: string;
    category?: {
      id: string;
      name: string;
      type: 'income' | 'expense';
      is_default: boolean;
    };
    created_by_user?: {
      id: string;
      full_name: string;
      email: string;
    };
  };
  currency: string;
  showUser?: boolean;
  className?: string;
}

// Currency symbols mapping
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  NPR: 'Rs.',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
};

function formatCurrency(amount: number, currency: string): string {
  const symbol = CURRENCY_SYMBOLS[currency] || currency;
  const formatted = amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return `${symbol}${formatted}`;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function getCategoryBadgeColor(type: 'income' | 'expense', isDefault: boolean): string {
  const baseColor = type === 'income' 
    ? 'bg-green-100 text-green-800 border-green-200'
    : 'bg-red-100 text-red-800 border-red-200';
  
  return baseColor;
}

export default function TransactionPreview({
  transaction,
  currency,
  showUser = false,
  className = '',
}: TransactionPreviewProps) {
  const {
    amount,
    type,
    description,
    date,
    category,
    created_by_user,
  } = transaction;

  const isIncome = type === 'income';
  const amountColor = isIncome ? 'text-green-600' : 'text-red-600';
  const icon = isIncome ? TrendingUp : TrendingDown;
  const IconComponent = icon;

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconComponent className={`w-5 h-5 ${isIncome ? 'text-green-600' : 'text-red-600'}`} />
          Transaction Preview
        </CardTitle>
        <CardDescription>
          Review the transaction details below
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Amount and Type */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">Amount</p>
            <div className="flex items-center gap-2">
              <p className={`text-3xl font-bold ${amountColor}`}>
                {isIncome ? '+' : '-'}{formatCurrency(amount, currency)}
              </p>
              <Badge 
                variant="outline" 
                className={`${isIncome ? 'border-green-200 text-green-700' : 'border-red-200 text-red-700'}`}
              >
                {type}
              </Badge>
            </div>
          </div>
          <div className={`p-4 rounded-lg ${isIncome ? 'bg-green-100' : 'bg-red-100'}`}>
            <IconComponent className={`w-8 h-8 ${isIncome ? 'text-green-600' : 'text-red-600'}`} />
          </div>
        </div>

        {/* Category */}
        {category && (
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Category
            </p>
            <div className="flex items-center gap-2">
              <Badge 
                variant="outline" 
                className={`${getCategoryBadgeColor(category.type, category.is_default)}`}
              >
                {category.name}
              </Badge>
              {category.is_default && (
                <Badge variant="secondary" className="text-xs">
                  Default
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Date */}
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Date
          </p>
          <p className="font-medium">{formatDate(date)}</p>
        </div>

        {/* Description */}
        {description && (
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Description
            </p>
            <p className="text-sm bg-muted p-3 rounded-lg">{description}</p>
          </div>
        )}

        {/* Created By User */}
        {showUser && created_by_user && (
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <User className="w-4 h-4" />
              Created By
            </p>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-primary">
                  {created_by_user.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium">{created_by_user.full_name}</p>
                <p className="text-xs text-muted-foreground">{created_by_user.email}</p>
              </div>
            </div>
          </div>
        )}

        {/* Summary */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Transaction Type</p>
              <p className="font-medium capitalize">{type}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Currency</p>
              <p className="font-medium">{currency}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
