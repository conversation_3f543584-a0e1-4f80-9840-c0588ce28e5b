"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  AlertCircle, 
  XCircle, 
  Wifi, 
  RefreshCw, 
  Home,
  Shield,
  Clock,
  Server
} from 'lucide-react';

export interface ErrorInfo {
  message: string;
  code?: string;
  type?: 'network' | 'validation' | 'permission' | 'server' | 'timeout' | 'unknown';
  details?: string;
  retryable?: boolean;
}

interface ErrorDisplayProps {
  error: ErrorInfo | Error | string;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  variant?: 'card' | 'alert' | 'inline';
  className?: string;
}

function normalizeError(error: ErrorInfo | Error | string): ErrorInfo {
  if (typeof error === 'string') {
    return {
      message: error,
      type: 'unknown',
      retryable: true,
    };
  }

  if (error instanceof Error) {
    return {
      message: error.message,
      type: 'unknown',
      details: error.stack,
      retryable: true,
    };
  }

  return error;
}

function getErrorIcon(type: ErrorInfo['type']) {
  switch (type) {
    case 'network':
      return Wifi;
    case 'validation':
      return AlertCircle;
    case 'permission':
      return Shield;
    case 'server':
      return Server;
    case 'timeout':
      return Clock;
    default:
      return AlertTriangle;
  }
}

function getErrorColor(type: ErrorInfo['type']) {
  switch (type) {
    case 'network':
      return 'text-blue-600';
    case 'validation':
      return 'text-yellow-600';
    case 'permission':
      return 'text-orange-600';
    case 'server':
      return 'text-red-600';
    case 'timeout':
      return 'text-purple-600';
    default:
      return 'text-red-600';
  }
}

function getErrorBgColor(type: ErrorInfo['type']) {
  switch (type) {
    case 'network':
      return 'bg-blue-50 border-blue-200';
    case 'validation':
      return 'bg-yellow-50 border-yellow-200';
    case 'permission':
      return 'bg-orange-50 border-orange-200';
    case 'server':
      return 'bg-red-50 border-red-200';
    case 'timeout':
      return 'bg-purple-50 border-purple-200';
    default:
      return 'bg-red-50 border-red-200';
  }
}

function getErrorTitle(type: ErrorInfo['type']) {
  switch (type) {
    case 'network':
      return 'Connection Error';
    case 'validation':
      return 'Validation Error';
    case 'permission':
      return 'Permission Denied';
    case 'server':
      return 'Server Error';
    case 'timeout':
      return 'Request Timeout';
    default:
      return 'Error';
  }
}

export default function ErrorDisplay({
  error,
  onRetry,
  onDismiss,
  showDetails = false,
  variant = 'card',
  className = '',
}: ErrorDisplayProps) {
  const errorInfo = normalizeError(error);
  const Icon = getErrorIcon(errorInfo.type);
  const iconColor = getErrorColor(errorInfo.type);
  const bgColor = getErrorBgColor(errorInfo.type);
  const title = getErrorTitle(errorInfo.type);

  if (variant === 'alert') {
    return (
      <Alert className={`${bgColor} ${className}`}>
        <Icon className={`h-4 w-4 ${iconColor}`} />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription className="mt-2">
          <p>{errorInfo.message}</p>
          {showDetails && errorInfo.details && (
            <details className="mt-2">
              <summary className="cursor-pointer text-sm font-medium">
                Error Details
              </summary>
              <pre className="mt-1 text-xs bg-white/50 p-2 rounded border overflow-auto max-h-32">
                {errorInfo.details}
              </pre>
            </details>
          )}
          {(onRetry || onDismiss) && (
            <div className="flex gap-2 mt-3">
              {onRetry && errorInfo.retryable && (
                <Button size="sm" variant="outline" onClick={onRetry}>
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Retry
                </Button>
              )}
              {onDismiss && (
                <Button size="sm" variant="ghost" onClick={onDismiss}>
                  Dismiss
                </Button>
              )}
            </div>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={`flex items-center gap-2 text-sm ${iconColor} ${className}`}>
        <Icon className="w-4 h-4" />
        <span>{errorInfo.message}</span>
        {onRetry && errorInfo.retryable && (
          <Button size="sm" variant="ghost" onClick={onRetry} className="h-6 px-2">
            <RefreshCw className="w-3 h-3" />
          </Button>
        )}
      </div>
    );
  }

  // Card variant (default)
  return (
    <Card className={`${bgColor} ${className}`}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Icon className={`w-5 h-5 ${iconColor}`} />
            <CardTitle className={`text-lg ${iconColor}`}>{title}</CardTitle>
          </div>
          {onDismiss && (
            <Button size="sm" variant="ghost" onClick={onDismiss}>
              <XCircle className="w-4 h-4" />
            </Button>
          )}
        </div>
        {errorInfo.code && (
          <CardDescription>Error Code: {errorInfo.code}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <p className="text-sm mb-4">{errorInfo.message}</p>
        
        {showDetails && errorInfo.details && (
          <details className="mb-4">
            <summary className="cursor-pointer text-sm font-medium text-muted-foreground">
              Technical Details
            </summary>
            <pre className="mt-2 text-xs bg-white/50 p-3 rounded border overflow-auto max-h-40">
              {errorInfo.details}
            </pre>
          </details>
        )}

        {(onRetry || onDismiss) && (
          <div className="flex gap-2">
            {onRetry && errorInfo.retryable && (
              <Button size="sm" onClick={onRetry}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            )}
            <Button size="sm" variant="outline" onClick={() => window.location.href = '/'}>
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Specific error components for common scenarios
export function NetworkError({ onRetry, className = '' }: { onRetry?: () => void; className?: string }) {
  return (
    <ErrorDisplay
      error={{
        message: 'Unable to connect to the server. Please check your internet connection.',
        type: 'network',
        retryable: true,
      }}
      onRetry={onRetry}
      className={className}
    />
  );
}

export function PermissionError({ message, className = '' }: { message?: string; className?: string }) {
  return (
    <ErrorDisplay
      error={{
        message: message || 'You do not have permission to access this resource.',
        type: 'permission',
        retryable: false,
      }}
      className={className}
    />
  );
}

export function ValidationError({ 
  errors, 
  className = '' 
}: { 
  errors: Record<string, string> | string; 
  className?: string;
}) {
  const message = typeof errors === 'string' 
    ? errors 
    : Object.values(errors).join(', ');

  return (
    <ErrorDisplay
      error={{
        message,
        type: 'validation',
        retryable: false,
      }}
      variant="alert"
      className={className}
    />
  );
}

export function ServerError({ onRetry, className = '' }: { onRetry?: () => void; className?: string }) {
  return (
    <ErrorDisplay
      error={{
        message: 'A server error occurred. Please try again later.',
        type: 'server',
        retryable: true,
      }}
      onRetry={onRetry}
      className={className}
    />
  );
}

export function TimeoutError({ onRetry, className = '' }: { onRetry?: () => void; className?: string }) {
  return (
    <ErrorDisplay
      error={{
        message: 'The request timed out. Please try again.',
        type: 'timeout',
        retryable: true,
      }}
      onRetry={onRetry}
      className={className}
    />
  );
}

// Hook for error handling with automatic retry
export function useErrorHandler() {
  const [error, setError] = React.useState<ErrorInfo | null>(null);
  const [retryCount, setRetryCount] = React.useState(0);

  const handleError = React.useCallback((error: ErrorInfo | Error | string) => {
    setError(normalizeError(error));
  }, []);

  const retry = React.useCallback(() => {
    setRetryCount(prev => prev + 1);
    setError(null);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  return {
    error,
    retryCount,
    handleError,
    retry,
    clearError,
    hasError: error !== null,
  };
}

// Export all error components
export const ErrorComponents = {
  ErrorDisplay,
  NetworkError,
  PermissionError,
  ValidationError,
  ServerError,
  TimeoutError,
  useErrorHandler,
};
