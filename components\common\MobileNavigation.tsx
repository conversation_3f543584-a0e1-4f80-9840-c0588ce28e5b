"use client"

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { 
  Menu, 
  X, 
  Home, 
  Calculator, 
  Wallet, 
  Settings, 
  User,
  ChevronRight,
  Plus
} from 'lucide-react';
import { useIsMobile } from '@/lib/responsive-utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavigationItem[];
}

interface MobileNavigationProps {
  items: NavigationItem[];
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  onCreateCashbook?: () => void;
}

function MobileNavigation({
  items,
  user,
  onCreateCashbook
}: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const isMobile = useIsMobile();
  const pathname = usePathname();

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const active = isActive(item.href);

    return (
      <div key={item.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <div className="flex items-center">
          <Link
            href={item.href}
            onClick={() => !hasChildren && setIsOpen(false)}
            className={`flex-1 flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
              active 
                ? 'bg-primary text-primary-foreground' 
                : 'hover:bg-muted'
            }`}
          >
            <item.icon className="w-5 h-5" />
            <span className="font-medium">{item.label}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </Link>
          
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleExpanded(item.id)}
              className="ml-2 p-2"
            >
              <ChevronRight 
                className={`w-4 h-4 transition-transform ${
                  isExpanded ? 'rotate-90' : ''
                }`} 
              />
            </Button>
          )}
        </div>
        
        {hasChildren && isExpanded && (
          <div className="mt-2 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // Don't render on desktop
  if (!isMobile) {
    return null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="w-5 h-5" />
          <span className="sr-only">Open navigation menu</span>
        </Button>
      </SheetTrigger>
      
      <SheetContent side="left" className="w-80 p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold">Menu</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* User Profile */}
          {user && (
            <div className="p-4 border-b">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  {user.avatar ? (
                    <img 
                      src={user.avatar} 
                      alt={user.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-5 h-5 text-primary" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{user.name}</p>
                  <p className="text-sm text-muted-foreground truncate">{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          {onCreateCashbook && (
            <div className="p-4 border-b">
              <Button 
                onClick={() => {
                  onCreateCashbook();
                  setIsOpen(false);
                }}
                className="w-full flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Create Cashbook
              </Button>
            </div>
          )}

          {/* Navigation Items */}
          <div className="flex-1 overflow-y-auto p-4">
            <nav className="space-y-2">
              {items.map(item => renderNavigationItem(item))}
            </nav>
          </div>

          {/* Footer */}
          <div className="p-4 border-t">
            <Link
              href="/settings"
              onClick={() => setIsOpen(false)}
              className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                isActive('/settings') 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-muted'
              }`}
            >
              <Settings className="w-5 h-5" />
              <span className="font-medium">Settings</span>
            </Link>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

// Default navigation items for the cashbook app
export const defaultNavigationItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    icon: Home,
  },
  {
    id: 'calculator',
    label: 'Calculator',
    href: '/apps/calculator',
    icon: Calculator,
  },
  {
    id: 'cashbook',
    label: 'Cashbook',
    href: '/apps/cashbook',
    icon: Wallet,
    children: [
      {
        id: 'cashbook-list',
        label: 'My Cashbooks',
        href: '/apps/cashbook',
        icon: Wallet,
      },
      {
        id: 'cashbook-categories',
        label: 'Categories',
        href: '/apps/cashbook/categories',
        icon: Wallet,
      },
    ],
  },
];

// Hook for managing mobile navigation state
export function useMobileNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();

  const openNavigation = () => setIsOpen(true);
  const closeNavigation = () => setIsOpen(false);
  const toggleNavigation = () => setIsOpen(prev => !prev);

  // Auto-close on desktop
  React.useEffect(() => {
    if (!isMobile && isOpen) {
      setIsOpen(false);
    }
  }, [isMobile, isOpen]);

  return {
    isOpen,
    openNavigation,
    closeNavigation,
    toggleNavigation,
    isMobile,
  };
}

// Mobile-optimized bottom navigation
export function BottomNavigation({ 
  items,
  className = '' 
}: { 
  items: NavigationItem[];
  className?: string;
}) {
  const pathname = usePathname();
  const isMobile = useIsMobile();

  if (!isMobile) {
    return null;
  }

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-background border-t z-50 ${className}`}>
      <div className="flex items-center justify-around py-2">
        {items.slice(0, 5).map(item => {
          const active = isActive(item.href);
          return (
            <Link
              key={item.id}
              href={item.href}
              className={`flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors ${
                active 
                  ? 'text-primary' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <item.icon className="w-5 h-5" />
              <span className="text-xs font-medium">{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  {item.badge}
                </Badge>
              )}
            </Link>
          );
        })}
      </div>
    </div>
  );
}

// Named exports
export { MobileNavigation };

// Default export
export default MobileNavigation;

// Export all mobile navigation components
export const MobileNavigationComponents = {
  MobileNavigation,
  BottomNavigation,
  useMobileNavigation,
  defaultNavigationItems,
};
