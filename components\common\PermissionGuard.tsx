"use client"

import React from 'react';
import { Permission, CashbookRole } from '@/lib/permissions';
import { usePermissions } from '@/hooks/usePermissions';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Lock, Eye, Shield } from 'lucide-react';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  role?: CashbookRole;
  roles?: CashbookRole[];
  cashbookId?: string;
  requireAll?: boolean; // For multiple permissions, require all (default) or any
  fallback?: React.ReactNode;
  showFallback?: boolean;
  className?: string;
}

export default function PermissionGuard({
  children,
  permission,
  permissions = [],
  role,
  roles = [],
  cashbookId,
  requireAll = true,
  fallback,
  showFallback = true,
  className = '',
}: PermissionGuardProps) {
  const userPermissions = usePermissions({ cashbookId });
  
  // Check if user has required permissions
  const hasRequiredPermissions = React.useMemo(() => {
    // Check single permission
    if (permission) {
      return userPermissions.hasPermission(permission);
    }
    
    // Check multiple permissions
    if (permissions.length > 0) {
      return requireAll 
        ? userPermissions.hasAllPermissions(permissions)
        : userPermissions.hasAnyPermission(permissions);
    }
    
    // Check single role
    if (role) {
      return userPermissions.role === role;
    }
    
    // Check multiple roles
    if (roles.length > 0) {
      return roles.includes(userPermissions.role as CashbookRole);
    }
    
    // If no specific requirements, check if user has any access
    return userPermissions.hasAccess;
  }, [
    permission, 
    permissions, 
    role, 
    roles, 
    requireAll, 
    userPermissions
  ]);
  
  // If user has required permissions, render children
  if (hasRequiredPermissions) {
    return <div className={className}>{children}</div>;
  }
  
  // If no fallback should be shown, render nothing
  if (!showFallback) {
    return null;
  }
  
  // If custom fallback is provided, use it
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }
  
  // Default fallback based on the type of restriction
  const getDefaultFallback = () => {
    if (!userPermissions.hasAccess) {
      return (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <Lock className="w-4 h-4" />
              <p className="text-sm font-medium">Access Denied</p>
            </div>
            <p className="text-sm text-red-600 mt-1">
              You don't have access to this cashbook.
            </p>
          </CardContent>
        </Card>
      );
    }
    
    if (userPermissions.isReadOnly) {
      return (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <Eye className="w-4 h-4" />
              <p className="text-sm font-medium">View Only</p>
            </div>
            <p className="text-sm text-yellow-600 mt-1">
              You have read-only access to this feature.
            </p>
          </CardContent>
        </Card>
      );
    }
    
    return (
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-orange-800">
            <Shield className="w-4 h-4" />
            <p className="text-sm font-medium">Insufficient Permissions</p>
          </div>
          <p className="text-sm text-orange-600 mt-1">
            You don't have permission to access this feature.
          </p>
        </CardContent>
      </Card>
    );
  };
  
  return <div className={className}>{getDefaultFallback()}</div>;
}

// Convenience components for common permission checks
export function OwnerOnly({ 
  children, 
  cashbookId, 
  fallback, 
  showFallback = true 
}: {
  children: React.ReactNode;
  cashbookId?: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGuard
      role="owner"
      cashbookId={cashbookId}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </PermissionGuard>
  );
}

export function EditorOrOwner({ 
  children, 
  cashbookId, 
  fallback, 
  showFallback = true 
}: {
  children: React.ReactNode;
  cashbookId?: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGuard
      roles={['owner', 'editor']}
      cashbookId={cashbookId}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </PermissionGuard>
  );
}

export function ViewerOrAbove({ 
  children, 
  cashbookId, 
  fallback, 
  showFallback = true 
}: {
  children: React.ReactNode;
  cashbookId?: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}) {
  return (
    <PermissionGuard
      roles={['owner', 'editor', 'viewer']}
      cashbookId={cashbookId}
      fallback={fallback}
      showFallback={showFallback}
    >
      {children}
    </PermissionGuard>
  );
}

// Hook for conditional rendering based on permissions
export function useConditionalRender(
  permission?: Permission,
  permissions?: Permission[],
  role?: CashbookRole,
  roles?: CashbookRole[],
  cashbookId?: string,
  requireAll: boolean = true
) {
  const userPermissions = usePermissions({ cashbookId });
  
  return React.useMemo(() => {
    // Check single permission
    if (permission) {
      return userPermissions.hasPermission(permission);
    }
    
    // Check multiple permissions
    if (permissions && permissions.length > 0) {
      return requireAll 
        ? userPermissions.hasAllPermissions(permissions)
        : userPermissions.hasAnyPermission(permissions);
    }
    
    // Check single role
    if (role) {
      return userPermissions.role === role;
    }
    
    // Check multiple roles
    if (roles && roles.length > 0) {
      return roles.includes(userPermissions.role as CashbookRole);
    }
    
    // Default to checking if user has any access
    return userPermissions.hasAccess;
  }, [
    permission, 
    permissions, 
    role, 
    roles, 
    requireAll, 
    userPermissions
  ]);
}

// Component for showing different content based on role
export function RoleBasedContent({
  ownerContent,
  editorContent,
  viewerContent,
  noAccessContent,
  cashbookId,
}: {
  ownerContent?: React.ReactNode;
  editorContent?: React.ReactNode;
  viewerContent?: React.ReactNode;
  noAccessContent?: React.ReactNode;
  cashbookId?: string;
}) {
  const permissions = usePermissions({ cashbookId });
  
  if (!permissions.hasAccess) {
    return noAccessContent || (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-800">
            <AlertCircle className="w-4 h-4" />
            <p className="text-sm font-medium">No Access</p>
          </div>
          <p className="text-sm text-red-600 mt-1">
            You don't have access to this cashbook.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  switch (permissions.role) {
    case 'owner':
      return ownerContent || editorContent || viewerContent || null;
    case 'editor':
      return editorContent || viewerContent || null;
    case 'viewer':
      return viewerContent || null;
    default:
      return noAccessContent || null;
  }
}

// Export all permission components
export const PermissionComponents = {
  PermissionGuard,
  OwnerOnly,
  EditorOrOwner,
  ViewerOrAbove,
  RoleBasedContent,
  useConditionalRender,
};
