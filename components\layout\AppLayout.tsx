"use client"

import React from 'react';
import { usePathname } from 'next/navigation';
import { CashbookProvider } from '@/contexts/CashbookContext';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { MobileNavigation, BottomNavigation, defaultNavigationItems } from '@/components/common/MobileNavigation';
import { useIsMobile } from '@/lib/responsive-utils';
import { Toaster } from '@/components/ui/toaster';

interface AppLayoutProps {
  children: React.ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();
  const isMobile = useIsMobile();
  
  // Determine if we're in the cashbook app
  const isCashbookApp = pathname.startsWith('/apps/cashbook');
  
  // Mock user data (replace with actual user context)
  const user = {
    name: '<PERSON>',
    email: '<EMAIL>',
  };

  const handleCreateCashbook = () => {
    // Navigate to create cashbook
    window.location.href = '/apps/cashbook?create=true';
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-background">
        {/* Mobile Navigation */}
        {isMobile && (
          <div className="sticky top-0 z-50 bg-background border-b">
            <div className="flex items-center justify-between p-4">
              <MobileNavigation
                items={defaultNavigationItems}
                user={user}
                onCreateCashbook={isCashbookApp ? handleCreateCashbook : undefined}
              />
              <h1 className="text-lg font-semibold">
                {isCashbookApp ? 'Cashbook' : 'Kanban Board'}
              </h1>
              <div className="w-10" /> {/* Spacer for centering */}
            </div>
          </div>
        )}

        {/* Main Content */}
        <main className={`${isMobile ? 'pb-20' : ''}`}>
          {isCashbookApp ? (
            <CashbookProvider>
              {children}
            </CashbookProvider>
          ) : (
            children
          )}
        </main>

        {/* Bottom Navigation for Mobile */}
        {isMobile && (
          <BottomNavigation items={defaultNavigationItems} />
        )}

        {/* Toast Notifications */}
        <Toaster />
      </div>
    </ErrorBoundary>
  );
}

// HOC for wrapping pages with app layout
export function withAppLayout<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <AppLayout>
      <Component {...props} />
    </AppLayout>
  );

  WrappedComponent.displayName = `withAppLayout(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Layout variants for different app sections
export function CashbookLayout({ children }: { children: React.ReactNode }) {
  return (
    <CashbookProvider>
      <ErrorBoundary>
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </ErrorBoundary>
    </CashbookProvider>
  );
}

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          {children}
        </div>
      </div>
    </ErrorBoundary>
  );
}
