"use client"

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { useAuth } from '@/components/auth-provider';
import {
  Cashbook,
  Transaction,
  Category,
  CashbookCollaborator,
  FinancialSummary,
  CashbookContextState,
  CashbookAction,
  CashbookActionType,
  LoadingState,
  UIState,
  CreateCashbookData,
  UpdateCashbookData,
  CreateTransactionData,
  UpdateTransactionData,
  CreateCategoryData,
  InviteCollaboratorData,
  UpdateCollaboratorData,
  TransactionFilters,
  CashbookError,
  CashbookErrorType,
  ApiResponse,
} from '@/types/cashbook';
import { CashbookUtils } from '@/lib/cashbook-utils';

// Initial state
const initialLoadingState: LoadingState = {
  cashbooks: false,
  transactions: false,
  categories: false,
  collaborators: false,
  creating: false,
  updating: false,
  deleting: false,
};

const initialUIState: UIState = {
  selectedCashbook: null,
  selectedTransaction: null,
  showCreateModal: false,
  showEditModal: false,
  showDeleteModal: false,
  showCollaboratorModal: false,
  filters: {},
  pagination: { page: 1, limit: 20 },
};

const initialState: CashbookContextState = {
  cashbooks: [],
  currentCashbook: null,
  transactions: [],
  categories: [],
  collaborators: [],
  loading: initialLoadingState,
  error: null,
  ui: initialUIState,
  financialSummary: null,
};

// Reducer function
function cashbookReducer(state: CashbookContextState, action: CashbookAction): CashbookContextState {
  switch (action.type) {
    case CashbookActionType.SET_LOADING:
      return {
        ...state,
        loading: { ...state.loading, ...action.payload },
      };

    case CashbookActionType.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };

    case CashbookActionType.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case CashbookActionType.SET_CASHBOOKS:
      return {
        ...state,
        cashbooks: action.payload,
      };

    case CashbookActionType.SET_CURRENT_CASHBOOK:
      return {
        ...state,
        currentCashbook: action.payload,
        ui: { ...state.ui, selectedCashbook: action.payload?.id || null },
      };

    case CashbookActionType.ADD_CASHBOOK:
      return {
        ...state,
        cashbooks: [...state.cashbooks, action.payload],
      };

    case CashbookActionType.UPDATE_CASHBOOK:
      return {
        ...state,
        cashbooks: state.cashbooks.map(cb =>
          cb.id === action.payload.id ? { ...cb, ...action.payload } : cb
        ),
        currentCashbook: state.currentCashbook?.id === action.payload.id
          ? { ...state.currentCashbook, ...action.payload }
          : state.currentCashbook,
      };

    case CashbookActionType.DELETE_CASHBOOK:
      return {
        ...state,
        cashbooks: state.cashbooks.filter(cb => cb.id !== action.payload),
        currentCashbook: state.currentCashbook?.id === action.payload ? null : state.currentCashbook,
      };

    case CashbookActionType.SET_TRANSACTIONS:
      return {
        ...state,
        transactions: action.payload,
      };

    case CashbookActionType.ADD_TRANSACTION:
      return {
        ...state,
        transactions: [...state.transactions, action.payload],
      };

    case CashbookActionType.UPDATE_TRANSACTION:
      return {
        ...state,
        transactions: state.transactions.map(t =>
          t.id === action.payload.id ? { ...t, ...action.payload } : t
        ),
      };

    case CashbookActionType.DELETE_TRANSACTION:
      return {
        ...state,
        transactions: state.transactions.filter(t => t.id !== action.payload),
      };

    case CashbookActionType.SET_CATEGORIES:
      return {
        ...state,
        categories: action.payload,
      };

    case CashbookActionType.ADD_CATEGORY:
      return {
        ...state,
        categories: [...state.categories, action.payload],
      };

    case CashbookActionType.SET_COLLABORATORS:
      return {
        ...state,
        collaborators: action.payload,
      };

    case CashbookActionType.ADD_COLLABORATOR:
      return {
        ...state,
        collaborators: [...state.collaborators, action.payload],
      };

    case CashbookActionType.UPDATE_COLLABORATOR:
      return {
        ...state,
        collaborators: state.collaborators.map(c =>
          c.id === action.payload.id ? { ...c, ...action.payload } : c
        ),
      };

    case CashbookActionType.DELETE_COLLABORATOR:
      return {
        ...state,
        collaborators: state.collaborators.filter(c => c.id !== action.payload),
      };

    case CashbookActionType.SET_UI_STATE:
      return {
        ...state,
        ui: { ...state.ui, ...action.payload },
      };

    case CashbookActionType.SET_FILTERS:
      return {
        ...state,
        ui: { ...state.ui, filters: { ...state.ui.filters, ...action.payload } },
      };

    case CashbookActionType.SET_PAGINATION:
      return {
        ...state,
        ui: { ...state.ui, pagination: { ...state.ui.pagination, ...action.payload } },
      };

    case CashbookActionType.SET_FINANCIAL_SUMMARY:
      return {
        ...state,
        financialSummary: action.payload,
      };

    default:
      return state;
  }
}

// Context interface
interface CashbookContextType extends CashbookContextState {
  // Cashbook actions
  loadCashbooks: () => Promise<void>;
  createCashbook: (data: CreateCashbookData) => Promise<Cashbook | null>;
  updateCashbook: (id: string, data: UpdateCashbookData) => Promise<Cashbook | null>;
  deleteCashbook: (id: string) => Promise<boolean>;
  selectCashbook: (cashbook: Cashbook | null) => void;

  // Transaction actions
  loadTransactions: (cashbookId: string) => Promise<void>;
  createTransaction: (data: CreateTransactionData) => Promise<Transaction | null>;
  updateTransaction: (id: string, data: UpdateTransactionData) => Promise<Transaction | null>;
  deleteTransaction: (id: string) => Promise<boolean>;

  // Category actions
  loadCategories: () => Promise<void>;
  createCategory: (data: CreateCategoryData) => Promise<Category | null>;

  // Collaborator actions
  loadCollaborators: (cashbookId: string) => Promise<void>;
  inviteCollaborator: (data: InviteCollaboratorData) => Promise<CashbookCollaborator | null>;
  updateCollaborator: (id: string, data: UpdateCollaboratorData) => Promise<CashbookCollaborator | null>;
  removeCollaborator: (id: string) => Promise<boolean>;

  // UI actions
  setFilters: (filters: Partial<TransactionFilters>) => void;
  clearFilters: () => void;
  setUIState: (state: Partial<UIState>) => void;
  clearError: () => void;

  // Utility functions
  getFilteredTransactions: () => Transaction[];
  calculateFinancialSummary: () => FinancialSummary | null;
  getUserRole: (cashbookId: string) => string | null;
  hasPermission: (action: string, cashbookId: string) => boolean;
}

// Create context
const CashbookContext = createContext<CashbookContextType | undefined>(undefined);

// Provider component
export function CashbookProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cashbookReducer, initialState);
  const { user } = useAuth();

  // Helper function to handle API errors
  const handleApiError = useCallback((error: any): CashbookError => {
    console.error('Cashbook API Error:', error);
    
    if (error.response?.data?.error) {
      return error.response.data.error;
    }
    
    return {
      type: CashbookErrorType.NETWORK_ERROR,
      message: error.message || 'An unexpected error occurred',
    };
  }, []);

  // Helper function to make API calls
  const apiCall = useCallback(async (
    url: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<any>> => {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      throw handleApiError(error);
    }
  }, [handleApiError]);

  // Load cashbooks
  const loadCashbooks = useCallback(async () => {
    if (!user) return;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { cashbooks: true } });
    dispatch({ type: CashbookActionType.CLEAR_ERROR });

    try {
      const response = await apiCall('/api/cashbooks');
      if (response.success && response.data) {
        dispatch({ type: CashbookActionType.SET_CASHBOOKS, payload: response.data });
      }
    } catch (error) {
      dispatch({ type: CashbookActionType.SET_ERROR, payload: error as CashbookError });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { cashbooks: false } });
    }
  }, [user, apiCall]);

  // Create cashbook
  const createCashbook = useCallback(async (data: CreateCashbookData): Promise<Cashbook | null> => {
    if (!user) return null;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { creating: true } });
    dispatch({ type: CashbookActionType.CLEAR_ERROR });

    try {
      const response = await apiCall('/api/cashbooks', {
        method: 'POST',
        body: JSON.stringify(data),
      });

      if (response.success && response.data) {
        dispatch({ type: CashbookActionType.ADD_CASHBOOK, payload: response.data });
        return response.data;
      }
    } catch (error) {
      dispatch({ type: CashbookActionType.SET_ERROR, payload: error as CashbookError });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { creating: false } });
    }

    return null;
  }, [user, apiCall]);

  // Update cashbook
  const updateCashbook = useCallback(async (id: string, data: UpdateCashbookData): Promise<Cashbook | null> => {
    if (!user) return null;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { updating: true } });
    dispatch({ type: CashbookActionType.CLEAR_ERROR });

    try {
      const response = await apiCall(`/api/cashbooks/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });

      if (response.success && response.data) {
        dispatch({ type: CashbookActionType.UPDATE_CASHBOOK, payload: response.data });
        return response.data;
      }
    } catch (error) {
      dispatch({ type: CashbookActionType.SET_ERROR, payload: error as CashbookError });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { updating: false } });
    }

    return null;
  }, [user, apiCall]);

  // Delete cashbook
  const deleteCashbook = useCallback(async (id: string): Promise<boolean> => {
    if (!user) return false;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { deleting: true } });
    dispatch({ type: CashbookActionType.CLEAR_ERROR });

    try {
      const response = await apiCall(`/api/cashbooks/${id}`, {
        method: 'DELETE',
      });

      if (response.success) {
        dispatch({ type: CashbookActionType.DELETE_CASHBOOK, payload: id });
        return true;
      }
    } catch (error) {
      dispatch({ type: CashbookActionType.SET_ERROR, payload: error as CashbookError });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { deleting: false } });
    }

    return false;
  }, [user, apiCall]);

  // Select cashbook
  const selectCashbook = useCallback((cashbook: Cashbook | null) => {
    dispatch({ type: CashbookActionType.SET_CURRENT_CASHBOOK, payload: cashbook });
    
    // Load related data when a cashbook is selected
    if (cashbook) {
      loadTransactions(cashbook.id);
      loadCollaborators(cashbook.id);
    }
  }, []);

  // Load transactions
  const loadTransactions = useCallback(async (cashbookId: string) => {
    if (!user) return;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { transactions: true } });
    dispatch({ type: CashbookActionType.CLEAR_ERROR });

    try {
      const response = await apiCall(`/api/cashbooks/${cashbookId}/transactions`);
      if (response.success && response.data) {
        dispatch({
          type: CashbookActionType.SET_TRANSACTIONS,
          payload: response.data
        });

        // Calculate financial summary
        const summary = CashbookUtils.calculateFinancialSummary(response.data);
        dispatch({
          type: CashbookActionType.SET_FINANCIAL_SUMMARY,
          payload: summary
        });
      }
    } catch (error) {
      console.error('Error loading transactions:', error);
      dispatch({
        type: CashbookActionType.SET_ERROR,
        payload: error as CashbookError
      });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { transactions: false } });
    }
  }, [user, apiCall]);

  // Create transaction
  const createTransaction = useCallback(async (data: CreateTransactionData): Promise<Transaction | null> => {
    if (!user) return null;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { creating: true } });
    dispatch({ type: CashbookActionType.CLEAR_ERROR });

    try {
      const response = await apiCall(`/api/cashbooks/${data.cashbook_id}/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          type: data.type,
          category_id: data.category_id,
          description: data.description,
          date: data.date,
          payment_method: data.payment_method,
        }),
      });

      if (response.success && response.data) {
        const newTransaction = response.data;

        // Add the new transaction to the state
        dispatch({
          type: CashbookActionType.ADD_TRANSACTION,
          payload: newTransaction
        });

        // Recalculate financial summary
        const updatedTransactions = [...state.transactions, newTransaction];
        const summary = CashbookUtils.calculateFinancialSummary(updatedTransactions);
        dispatch({
          type: CashbookActionType.SET_FINANCIAL_SUMMARY,
          payload: summary
        });

        return newTransaction;
      } else {
        throw new Error(response.error || 'Failed to create transaction');
      }
    } catch (error) {
      console.error('Error creating transaction:', error);
      dispatch({
        type: CashbookActionType.SET_ERROR,
        payload: error as CashbookError
      });
      throw error;
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { creating: false } });
    }
  }, [user, apiCall, state.transactions]);

  const updateTransaction = useCallback(async (id: string, data: UpdateTransactionData): Promise<Transaction | null> => {
    console.log('Updating transaction:', id, data);
    return null;
  }, []);

  const deleteTransaction = useCallback(async (id: string): Promise<boolean> => {
    console.log('Deleting transaction:', id);
    return false;
  }, []);

  const loadCategories = useCallback(async () => {
    if (!user) return;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { categories: true } });

    try {
      const response = await fetch('/api/categories');
      const result = await response.json();

      if (result.success) {
        dispatch({
          type: CashbookActionType.SET_CATEGORIES,
          payload: result.data
        });
      } else {
        throw new Error(result.error || 'Failed to load categories');
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      dispatch({
        type: CashbookActionType.SET_ERROR,
        payload: 'Failed to load categories'
      });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { categories: false } });
    }
  }, [user]);

  const createCategory = useCallback(async (data: CreateCategoryData): Promise<Category | null> => {
    if (!user) return null;

    dispatch({ type: CashbookActionType.SET_LOADING, payload: { creating: true } });

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        dispatch({
          type: CashbookActionType.ADD_CATEGORY,
          payload: result.data
        });
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      dispatch({
        type: CashbookActionType.SET_ERROR,
        payload: 'Failed to create category'
      });
    } finally {
      dispatch({ type: CashbookActionType.SET_LOADING, payload: { creating: false } });
    }

    return null;
  }, [user]);

  const loadCollaborators = useCallback(async (cashbookId: string) => {
    console.log('Loading collaborators for cashbook:', cashbookId);
  }, []);

  const inviteCollaborator = useCallback(async (data: InviteCollaboratorData): Promise<CashbookCollaborator | null> => {
    console.log('Inviting collaborator:', data);
    return null;
  }, []);

  const updateCollaborator = useCallback(async (id: string, data: UpdateCollaboratorData): Promise<CashbookCollaborator | null> => {
    console.log('Updating collaborator:', id, data);
    return null;
  }, []);

  const removeCollaborator = useCallback(async (id: string): Promise<boolean> => {
    console.log('Removing collaborator:', id);
    return false;
  }, []);

  // UI actions
  const setFilters = useCallback((filters: Partial<TransactionFilters>) => {
    dispatch({ type: CashbookActionType.SET_FILTERS, payload: filters });
  }, []);

  const clearFilters = useCallback(() => {
    dispatch({ type: CashbookActionType.SET_FILTERS, payload: {} });
  }, []);

  const setUIState = useCallback((uiState: Partial<UIState>) => {
    dispatch({ type: CashbookActionType.SET_UI_STATE, payload: uiState });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: CashbookActionType.CLEAR_ERROR });
  }, []);

  // Utility functions
  const getFilteredTransactions = useCallback((): Transaction[] => {
    return CashbookUtils.filterTransactions(state.transactions, state.ui.filters);
  }, [state.transactions, state.ui.filters]);

  const calculateFinancialSummary = useCallback((): FinancialSummary | null => {
    if (!state.currentCashbook) return null;
    
    const filteredTransactions = getFilteredTransactions();
    return CashbookUtils.calculateFinancialSummary(filteredTransactions, state.currentCashbook.currency);
  }, [state.currentCashbook, getFilteredTransactions]);

  const getUserRole = useCallback((cashbookId: string): string | null => {
    if (!user) return null;
    
    const cashbook = state.cashbooks.find(cb => cb.id === cashbookId);
    if (!cashbook) return null;
    
    return CashbookUtils.getUserRole(cashbook, user.id, state.collaborators);
  }, [user, state.cashbooks, state.collaborators]);

  const hasPermission = useCallback((action: string, cashbookId: string): boolean => {
    const role = getUserRole(cashbookId);
    return CashbookUtils.hasPermission(action as any, role as any);
  }, [getUserRole]);

  // Load initial data when user is available
  useEffect(() => {
    if (user) {
      loadCashbooks();
      loadCategories();
    }
  }, [user, loadCashbooks, loadCategories]);

  // Calculate financial summary when transactions or filters change
  useEffect(() => {
    const summary = calculateFinancialSummary();
    dispatch({ type: CashbookActionType.SET_FINANCIAL_SUMMARY, payload: summary });
  }, [calculateFinancialSummary]);

  const contextValue: CashbookContextType = {
    ...state,
    loadCashbooks,
    createCashbook,
    updateCashbook,
    deleteCashbook,
    selectCashbook,
    loadTransactions,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    loadCategories,
    createCategory,
    loadCollaborators,
    inviteCollaborator,
    updateCollaborator,
    removeCollaborator,
    setFilters,
    clearFilters,
    setUIState,
    clearError,
    getFilteredTransactions,
    calculateFinancialSummary,
    getUserRole,
    hasPermission,
  };

  return (
    <CashbookContext.Provider value={contextValue}>
      {children}
    </CashbookContext.Provider>
  );
}

// Hook to use the context
export function useCashbook() {
  const context = useContext(CashbookContext);
  if (context === undefined) {
    throw new Error('useCashbook must be used within a CashbookProvider');
  }
  return context;
}
