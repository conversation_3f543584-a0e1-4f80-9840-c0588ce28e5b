# Authentication Setup Guide

This guide provides two authentication solutions for the Cashbook Management System.

## 🚀 Quick Start (Development)

For immediate development, the system is configured with **mock authentication** that allows you to start developing right away.

### Current Configuration
- Mock authentication is enabled by default
- Demo user: `<EMAIL>` / `password`
- All API routes work with mock sessions
- No additional setup required

## 🔐 Production Setup (NextAuth.js)

For production deployment, follow these steps to set up full authentication:

### Step 1: Install Dependencies

```bash
npm install next-auth @auth/prisma-adapter
npm install bcryptjs @types/bcryptjs
npm install jsonwebtoken @types/jsonwebtoken
```

### Step 2: Environment Variables

Add to your `.env.local`:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-here-make-it-long-and-random

# OAuth Providers (optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Generate secret with: openssl rand -base64 32
```

### Step 3: Switch to Production Authentication

In your API route files, change the import from:

```typescript
// Current (mock)
import { getServerSession, authOptions } from '@/lib/mock-auth';
```

To:

```typescript
// Production (NextAuth.js)
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
```

Update these files:
- `app/api/cashbooks/route.ts`
- `app/api/cashbooks/[id]/route.ts`
- `app/api/cashbooks/[id]/transactions/route.ts`
- `app/api/categories/route.ts`

### Step 4: Update Middleware

In `middleware.ts`, uncomment the NextAuth.js section and comment out the development section.

### Step 5: Add Session Provider

Wrap your app with the session provider in `app/layout.tsx`:

```typescript
import SessionProvider from '@/components/providers/SessionProvider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <SessionProvider>
          {children}
        </SessionProvider>
      </body>
    </html>
  );
}
```

## 🔧 Database Integration

### User Table Schema

Ensure your database has a users table:

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  password_hash VARCHAR(255), -- For credentials provider
  profile_image TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Update Auth Configuration

In `lib/auth.ts`, replace the mock user functions with real database queries:

```typescript
async function getUserByEmail(email: string) {
  const { run_sql_neon } = await import('./neon');
  const result = await run_sql_neon({
    sql: 'SELECT id, email, full_name as name, password_hash FROM users WHERE email = $1',
    params: [email],
  });
  return result.rows[0];
}

async function createUser(email: string, name: string, image?: string) {
  const { run_sql_neon } = await import('./neon');
  const result = await run_sql_neon({
    sql: 'INSERT INTO users (email, full_name, profile_image) VALUES ($1, $2, $3) RETURNING id, email, full_name as name, profile_image as image',
    params: [email, name, image || null],
  });
  return result.rows[0];
}
```

## 🎯 Authentication Flow

### Sign In Process
1. User visits `/auth/signin`
2. Enters credentials or uses OAuth
3. NextAuth.js validates credentials
4. Session is created and stored
5. User is redirected to `/apps/cashbook`

### API Protection
1. API routes check for valid session
2. Extract user ID from session
3. Use user ID for database queries
4. Return appropriate data based on permissions

### Route Protection
1. Middleware checks for valid session
2. Redirects unauthenticated users to sign-in
3. Allows access to protected routes

## 🔄 Switching Between Auth Systems

### Development (Mock Auth)
```typescript
// In API routes
import { getServerSession, authOptions } from '@/lib/mock-auth';
```

### Production (NextAuth.js)
```typescript
// In API routes
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
```

## 🧪 Testing Authentication

### Mock Authentication Testing
- Always returns valid session in development
- User ID: `mock-user-1`
- Email: `<EMAIL>`
- Name: `Demo User`

### Production Authentication Testing
1. Set up test OAuth apps (Google/GitHub)
2. Create test user accounts
3. Test sign-in/sign-out flows
4. Verify session persistence
5. Test API route protection

## 🔒 Security Considerations

### Development
- Mock auth is for development only
- Never use in production
- No real security validation

### Production
- Use strong NEXTAUTH_SECRET
- Enable HTTPS in production
- Configure OAuth apps properly
- Implement proper password hashing
- Add rate limiting for auth endpoints

## 🚨 Troubleshooting

### Common Issues

1. **Module not found: 'next-auth'**
   - Run: `npm install next-auth`

2. **NEXTAUTH_SECRET missing**
   - Add to `.env.local`: `NEXTAUTH_SECRET=your-secret-here`

3. **OAuth provider errors**
   - Check client ID/secret configuration
   - Verify redirect URLs in OAuth app settings

4. **Session not persisting**
   - Check cookie settings
   - Verify NEXTAUTH_URL matches your domain

### Debug Mode

Enable debug mode in development:

```typescript
// In lib/auth.ts
export const authOptions: NextAuthOptions = {
  // ... other config
  debug: process.env.NODE_ENV === 'development',
};
```

## 📚 Additional Resources

- [NextAuth.js Documentation](https://next-auth.js.org/)
- [OAuth Provider Setup](https://next-auth.js.org/providers/)
- [Database Adapters](https://next-auth.js.org/adapters/)
- [Security Best Practices](https://next-auth.js.org/security/)

## 🎉 Quick Commands

```bash
# Install NextAuth.js
npm install next-auth

# Generate secret
openssl rand -base64 32

# Start development server
npm run dev

# Build for production
npm run build
```
