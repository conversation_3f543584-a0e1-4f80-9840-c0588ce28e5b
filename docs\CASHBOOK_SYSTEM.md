# Cashbook Management System

A comprehensive financial management system built with Next.js, TypeScript, and modern web technologies.

## 🚀 Features

### Core Functionality
- **Multi-Cashbook Management**: Create and manage multiple cashbooks for different purposes
- **Transaction Tracking**: Add, edit, and delete income/expense transactions
- **Category Management**: Organize transactions with default and custom categories
- **Financial Overview**: Real-time financial summaries and analytics
- **Collaboration**: Invite users with role-based permissions (Owner, Editor, Viewer)

### User Experience
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Touch-Friendly**: Mobile-first design with touch-optimized interactions
- **Quick Actions**: One-click transaction creation for common expenses
- **Advanced Filtering**: Search and filter transactions by multiple criteria
- **Real-time Updates**: Optimistic UI updates for better perceived performance

### Technical Features
- **Permission System**: Comprehensive role-based access control
- **Error Handling**: Graceful error handling with user-friendly messages
- **Loading States**: Skeleton screens and loading indicators
- **Accessibility**: WCAG 2.1 AA compliant with screen reader support
- **Testing**: Comprehensive E2E testing with Playwright

## 🏗️ Architecture

### Frontend Stack
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Modern UI component library
- **React Context**: State management for cashbook data

### Backend Integration
- **Neon Database**: PostgreSQL database with RLS policies
- **Next.js API Routes**: RESTful API endpoints
- **Authentication**: NextAuth.js integration ready

### Testing & Quality
- **Playwright**: End-to-end testing
- **TypeScript**: Compile-time type checking
- **ESLint**: Code linting and formatting

## 📁 Project Structure

```
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   ├── cashbooks/           # Cashbook CRUD operations
│   │   └── categories/          # Category management
│   └── apps/cashbook/           # Cashbook application pages
├── components/                   # React components
│   ├── cashbook/                # Cashbook-specific components
│   ├── common/                  # Shared components
│   └── layout/                  # Layout components
├── contexts/                     # React contexts
├── hooks/                       # Custom React hooks
├── lib/                         # Utility libraries
├── tests/                       # E2E tests
└── types/                       # TypeScript type definitions
```

## 🎯 Key Components

### Core Components
- **CashbookListScreen**: Main dashboard with cashbook overview
- **CashbookDetailScreen**: Individual cashbook management
- **TransactionFormScreen**: Transaction creation/editing
- **FinancialOverview**: Financial metrics and summaries
- **CategorySelector**: Category selection with creation

### UI Components
- **TransactionItem**: Individual transaction display
- **CashbookCard**: Cashbook preview cards
- **QuickTransactionActions**: One-click transaction shortcuts
- **PermissionGuard**: Role-based component visibility

### Utility Components
- **ErrorBoundary**: Error handling and recovery
- **LoadingStates**: Skeleton screens and spinners
- **MobileNavigation**: Mobile-optimized navigation

## 🔐 Permission System

### Roles
- **Owner**: Full control over cashbook and collaborators
- **Editor**: Can add/edit transactions and categories
- **Viewer**: Read-only access to cashbook data

### Permission Checks
```typescript
import { usePermissions } from '@/hooks/usePermissions';

const permissions = usePermissions({ cashbookId });
if (permissions.canCreateTransaction) {
  // Show create transaction UI
}
```

## 📱 Responsive Design

### Breakpoints
- **xs**: < 640px (Mobile)
- **sm**: 640px+ (Large mobile)
- **md**: 768px+ (Tablet)
- **lg**: 1024px+ (Desktop)
- **xl**: 1280px+ (Large desktop)

### Mobile Optimizations
- Touch-friendly button sizes (44px minimum)
- Swipe gestures for navigation
- Bottom navigation for quick access
- Optimized form layouts for mobile keyboards

## 🧪 Testing

### E2E Testing with Playwright
```bash
# Run all tests
npm run test:e2e

# Run tests in headed mode
npm run test:e2e:headed

# Run specific test file
npx playwright test cashbook.spec.ts
```

### Test Coverage
- Cashbook CRUD operations
- Transaction management
- Permission-based access
- Mobile responsiveness
- Error handling
- Accessibility compliance

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL database (Neon recommended)
- Environment variables configured

### Installation
```bash
# Clone the repository
git clone <repository-url>

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Run database migrations
npm run db:migrate

# Start development server
npm run dev
```

### Environment Variables
```env
DATABASE_URL=your_neon_database_url
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

## 📊 Database Schema

### Core Tables
- **cashbooks**: Cashbook metadata and settings
- **transactions**: Financial transactions
- **categories**: Transaction categories
- **cashbook_collaborators**: User permissions
- **users**: User accounts and profiles

### Views
- **financial_summary**: Aggregated financial metrics
- **user_cashbook_access**: User access permissions

## 🎨 Styling Guidelines

### Design System
- **Primary Color**: Blue (#3b82f6)
- **Success Color**: Green (#22c55e)
- **Warning Color**: Yellow (#f59e0b)
- **Error Color**: Red (#ef4444)

### Component Patterns
- Cards for content grouping
- Consistent spacing (4px grid)
- Rounded corners (8px default)
- Subtle shadows for depth

## ♿ Accessibility

### WCAG 2.1 AA Compliance
- Keyboard navigation support
- Screen reader compatibility
- Color contrast ratios > 4.5:1
- Focus management
- ARIA labels and descriptions

### Testing Accessibility
```bash
# Run accessibility tests
npm run test:a11y

# Check color contrast
npm run test:contrast
```

## 🔧 Development

### Code Style
- TypeScript strict mode
- ESLint + Prettier
- Conventional commits
- Component-first architecture

### Performance
- Code splitting by route
- Image optimization
- Bundle size monitoring
- Lazy loading for heavy components

## 📈 Monitoring

### Error Tracking
- Error boundaries for graceful failures
- Client-side error logging
- Performance monitoring

### Analytics
- User interaction tracking
- Feature usage metrics
- Performance analytics

## 🚀 Deployment

### Production Build
```bash
# Build for production
npm run build

# Start production server
npm start
```

### Environment Setup
- Configure production database
- Set up authentication providers
- Configure monitoring and logging

## 🤝 Contributing

### Development Workflow
1. Create feature branch
2. Implement changes with tests
3. Run quality checks
4. Submit pull request
5. Code review and merge

### Quality Gates
- All tests passing
- TypeScript compilation
- ESLint checks
- Accessibility compliance
- Performance benchmarks

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Playwright Testing](https://playwright.dev/docs)
- [WCAG Guidelines](https://www.w3.org/WAG/WCAG21/quickref)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
