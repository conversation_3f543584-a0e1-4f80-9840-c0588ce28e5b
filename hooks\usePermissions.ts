// React Hook for Permission Management

import { useMemo } from 'react';
import { useCashbook } from '@/contexts/CashbookContext';
import { 
  Permission, 
  PermissionChecks, 
  getVisibleActions, 
  createPermissionContext,
  CashbookRole 
} from '@/lib/permissions';

interface UsePermissionsOptions {
  cashbookId?: string;
  fallbackRole?: CashbookRole | null;
}

export function usePermissions(options: UsePermissionsOptions = {}) {
  const { getUserRole } = useCashbook();
  const { cashbookId, fallbackRole = null } = options;
  
  // Get user role for the specified cashbook
  const userRole = useMemo(() => {
    if (cashbookId) {
      return getUserRole(cashbookId);
    }
    return fallbackRole;
  }, [cashbookId, getUserRole, fallbackRole]);
  
  // Create permission context
  const permissionContext = useMemo(() => {
    return createPermissionContext(userRole);
  }, [userRole]);
  
  // Convenience functions
  const hasPermission = useMemo(() => {
    return (permission: Permission) => {
      return permissionContext.permissions.includes(permission);
    };
  }, [permissionContext.permissions]);
  
  const hasAnyPermission = useMemo(() => {
    return (permissions: Permission[]) => {
      return permissions.some(permission => hasPermission(permission));
    };
  }, [hasPermission]);
  
  const hasAllPermissions = useMemo(() => {
    return (permissions: Permission[]) => {
      return permissions.every(permission => hasPermission(permission));
    };
  }, [hasPermission]);
  
  return {
    // Current user role
    role: userRole,
    
    // Permission checking functions
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // Convenience checks
    checks: permissionContext.checks,
    
    // UI action visibility
    actions: permissionContext.actions,
    
    // Role checks
    isOwner: userRole === 'owner',
    isEditor: userRole === 'editor',
    isViewer: userRole === 'viewer',
    canEdit: userRole === 'owner' || userRole === 'editor',
    isReadOnly: userRole === 'viewer',
    hasAccess: userRole !== null,
    
    // Permission context for passing to child components
    context: permissionContext,
  };
}

// Hook for specific cashbook permissions
export function useCashbookPermissions(cashbookId: string) {
  return usePermissions({ cashbookId });
}

// Hook for checking if user can perform specific actions
export function useActionPermissions(cashbookId?: string) {
  const permissions = usePermissions({ cashbookId });
  
  return useMemo(() => ({
    // Cashbook actions
    canViewCashbook: permissions.checks.canViewCashbook(permissions.role),
    canEditCashbook: permissions.checks.canEditCashbook(permissions.role),
    canDeleteCashbook: permissions.checks.canDeleteCashbook(permissions.role),
    canManageCashbook: permissions.checks.canManageCashbook(permissions.role),
    
    // Transaction actions
    canViewTransactions: permissions.checks.canViewTransactions(permissions.role),
    canCreateTransaction: permissions.checks.canCreateTransaction(permissions.role),
    canEditTransaction: permissions.checks.canEditTransaction(permissions.role),
    canDeleteTransaction: permissions.checks.canDeleteTransaction(permissions.role),
    canManageTransactions: permissions.checks.canManageTransactions(permissions.role),
    
    // Category actions
    canViewCategories: permissions.checks.canViewCategories(permissions.role),
    canCreateCategory: permissions.checks.canCreateCategory(permissions.role),
    canEditCategory: permissions.checks.canEditCategory(permissions.role),
    canDeleteCategory: permissions.checks.canDeleteCategory(permissions.role),
    canManageCategories: permissions.checks.canManageCategories(permissions.role),
    
    // Collaborator actions
    canViewCollaborators: permissions.checks.canViewCollaborators(permissions.role),
    canInviteCollaborators: permissions.checks.canInviteCollaborators(permissions.role),
    canManageCollaborators: permissions.checks.canManageCollaborators(permissions.role),
    canRemoveCollaborators: permissions.checks.canRemoveCollaborators(permissions.role),
    
    // Financial data actions
    canViewFinancialSummary: permissions.checks.canViewFinancialSummary(permissions.role),
    canExportData: permissions.checks.canExportData(permissions.role),
    
    // Settings actions
    canManageSettings: permissions.checks.canManageSettings(permissions.role),
  }), [permissions]);
}

// Hook for UI visibility based on permissions
export function useUIPermissions(cashbookId?: string) {
  const permissions = usePermissions({ cashbookId });
  
  return useMemo(() => ({
    // Button/action visibility
    showCreateButton: permissions.actions.showCreateTransaction,
    showEditButton: permissions.actions.showEditTransaction,
    showDeleteButton: permissions.actions.showDeleteTransaction,
    showManageButton: permissions.actions.showManageCollaborators,
    showInviteButton: permissions.actions.showInviteCollaborator,
    showExportButton: permissions.actions.showExportData,
    showSettingsButton: permissions.actions.showSettings,
    
    // Section visibility
    showQuickActions: permissions.actions.showQuickActions,
    showFinancialSummary: permissions.actions.showFinancialSummary,
    showCollaboratorSection: permissions.checks.canViewCollaborators(permissions.role),
    showCategoryManagement: permissions.checks.canManageCategories(permissions.role),
    
    // Form field visibility
    showAdvancedOptions: permissions.isOwner || permissions.isEditor,
    showAdminFields: permissions.isOwner,
    
    // Menu item visibility
    showCashbookMenu: permissions.checks.canViewCashbook(permissions.role),
    showTransactionMenu: permissions.checks.canViewTransactions(permissions.role),
    showReportsMenu: permissions.checks.canViewFinancialSummary(permissions.role),
    showSettingsMenu: permissions.checks.canManageSettings(permissions.role),
  }), [permissions]);
}

// Hook for permission-based styling
export function usePermissionStyling(cashbookId?: string) {
  const permissions = usePermissions({ cashbookId });
  
  return useMemo(() => ({
    // Role-based styling
    roleClass: `role-${permissions.role || 'none'}`,
    roleBadgeColor: permissions.role === 'owner' 
      ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
      : permissions.role === 'editor'
      ? 'bg-blue-100 text-blue-800 border-blue-200'
      : 'bg-gray-100 text-gray-800 border-gray-200',
    
    // Permission-based styling
    readOnlyClass: permissions.isReadOnly ? 'read-only' : '',
    editableClass: permissions.canEdit ? 'editable' : '',
    ownerClass: permissions.isOwner ? 'owner' : '',
    
    // Disabled states
    isDisabled: !permissions.hasAccess,
    isReadOnly: permissions.isReadOnly,
    canInteract: permissions.canEdit,
  }), [permissions]);
}

// Hook for permission error handling
export function usePermissionErrors() {
  return useMemo(() => ({
    getErrorMessage: (requiredRole: CashbookRole, currentRole: CashbookRole | null) => {
      if (!currentRole) {
        return 'You do not have access to this cashbook';
      }
      
      switch (requiredRole) {
        case 'owner':
          return 'Only the cashbook owner can perform this action';
        case 'editor':
          return 'You need editor permissions to perform this action';
        case 'viewer':
          return 'This action requires higher permissions';
        default:
          return 'You do not have permission to perform this action';
      }
    },
    
    getAccessDeniedMessage: (action: string) => {
      return `Access denied: You don't have permission to ${action}`;
    },
    
    getUpgradeMessage: (currentRole: CashbookRole | null, requiredRole: CashbookRole) => {
      return `Your current role (${currentRole || 'none'}) doesn't allow this action. ${requiredRole} access required.`;
    },
  }), []);
}

// Export all hooks
export const PermissionHooks = {
  usePermissions,
  useCashbookPermissions,
  useActionPermissions,
  useUIPermissions,
  usePermissionStyling,
  usePermissionErrors,
};
