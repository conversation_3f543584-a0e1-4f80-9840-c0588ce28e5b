// Accessibility utilities for the Cashbook Management System

import { useEffect, useRef } from 'react';

// ARIA live region announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

// Focus management utilities
export class FocusManager {
  private static focusStack: HTMLElement[] = [];
  
  static pushFocus(element: HTMLElement) {
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus) {
      this.focusStack.push(currentFocus);
    }
    element.focus();
  }
  
  static popFocus() {
    const previousFocus = this.focusStack.pop();
    if (previousFocus) {
      previousFocus.focus();
    }
  }
  
  static trapFocus(container: HTMLElement) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    
    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
}

// Hook for managing focus
export function useFocusManagement() {
  const elementRef = useRef<HTMLElement>(null);
  
  const focusElement = () => {
    if (elementRef.current) {
      elementRef.current.focus();
    }
  };
  
  const blurElement = () => {
    if (elementRef.current) {
      elementRef.current.blur();
    }
  };
  
  return {
    elementRef,
    focusElement,
    blurElement,
  };
}

// Hook for focus trap
export function useFocusTrap(isActive: boolean = true) {
  const containerRef = useRef<HTMLElement>(null);
  
  useEffect(() => {
    if (!isActive || !containerRef.current) return;
    
    const cleanup = FocusManager.trapFocus(containerRef.current);
    
    return cleanup;
  }, [isActive]);
  
  return containerRef;
}

// Keyboard navigation utilities
export const KeyboardNavigation = {
  // Handle arrow key navigation in lists
  handleArrowNavigation: (
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (index: number) => void
  ) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        onIndexChange(nextIndex);
        items[nextIndex]?.focus();
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        onIndexChange(prevIndex);
        items[prevIndex]?.focus();
        break;
        
      case 'Home':
        event.preventDefault();
        onIndexChange(0);
        items[0]?.focus();
        break;
        
      case 'End':
        event.preventDefault();
        const lastIndex = items.length - 1;
        onIndexChange(lastIndex);
        items[lastIndex]?.focus();
        break;
    }
  },
  
  // Handle escape key
  handleEscape: (callback: () => void) => (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      callback();
    }
  },
  
  // Handle enter/space activation
  handleActivation: (callback: () => void) => (event: KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      callback();
    }
  },
};

// Color contrast utilities
export const ColorContrast = {
  // Calculate relative luminance
  getLuminance: (r: number, g: number, b: number): number => {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },
  
  // Calculate contrast ratio
  getContrastRatio: (color1: [number, number, number], color2: [number, number, number]): number => {
    const lum1 = ColorContrast.getLuminance(...color1);
    const lum2 = ColorContrast.getLuminance(...color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },
  
  // Check if contrast meets WCAG standards
  meetsWCAG: (ratio: number, level: 'AA' | 'AAA' = 'AA'): boolean => {
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
  },
};

// Screen reader utilities
export const ScreenReader = {
  // Check if screen reader is active
  isActive: (): boolean => {
    return window.navigator.userAgent.includes('NVDA') ||
           window.navigator.userAgent.includes('JAWS') ||
           window.speechSynthesis?.speaking ||
           false;
  },
  
  // Announce status changes
  announceStatus: (message: string) => {
    announceToScreenReader(message, 'polite');
  },
  
  // Announce urgent information
  announceUrgent: (message: string) => {
    announceToScreenReader(message, 'assertive');
  },
  
  // Announce page changes
  announcePageChange: (pageName: string) => {
    announceToScreenReader(`Navigated to ${pageName}`, 'polite');
  },
};

// ARIA utilities
export const ARIA = {
  // Generate unique IDs for ARIA relationships
  generateId: (prefix: string = 'aria'): string => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  },
  
  // Common ARIA attributes
  attributes: {
    // For buttons
    button: (pressed?: boolean, expanded?: boolean) => ({
      role: 'button',
      'aria-pressed': pressed !== undefined ? pressed : undefined,
      'aria-expanded': expanded !== undefined ? expanded : undefined,
    }),
    
    // For form controls
    textbox: (invalid?: boolean, required?: boolean, describedBy?: string) => ({
      role: 'textbox',
      'aria-invalid': invalid || undefined,
      'aria-required': required || undefined,
      'aria-describedby': describedBy || undefined,
    }),
    
    // For lists
    list: () => ({
      role: 'list',
    }),
    
    listitem: () => ({
      role: 'listitem',
    }),
    
    // For navigation
    navigation: (label?: string) => ({
      role: 'navigation',
      'aria-label': label || undefined,
    }),
    
    // For regions
    region: (label: string) => ({
      role: 'region',
      'aria-label': label,
    }),
    
    // For status updates
    status: () => ({
      role: 'status',
      'aria-live': 'polite',
    }),
    
    alert: () => ({
      role: 'alert',
      'aria-live': 'assertive',
    }),
  },
};

// Reduced motion utilities
export const ReducedMotion = {
  // Check if user prefers reduced motion
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },
  
  // Get appropriate animation duration
  getAnimationDuration: (normalDuration: number): number => {
    return ReducedMotion.prefersReducedMotion() ? 0 : normalDuration;
  },
  
  // Get appropriate transition
  getTransition: (normalTransition: string): string => {
    return ReducedMotion.prefersReducedMotion() ? 'none' : normalTransition;
  },
};

// Hook for reduced motion
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);
  
  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  
  return prefersReducedMotion;
}

// Export all accessibility utilities
export const AccessibilityUtils = {
  announceToScreenReader,
  FocusManager,
  useFocusManagement,
  useFocusTrap,
  KeyboardNavigation,
  ColorContrast,
  ScreenReader,
  ARIA,
  ReducedMotion,
  useReducedMotion,
};
