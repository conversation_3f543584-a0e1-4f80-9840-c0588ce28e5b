import { NextAuthOptions } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import bcrypt from 'bcryptjs';

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      image?: string;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    image?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
  }
}

// Mock user database (replace with actual database queries)
const users = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Demo User',
    password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LrUpe', // 'password'
  },
];

async function getUserByEmail(email: string) {
  // Replace with actual database query
  // Example with your Neon database:
  /*
  const { run_sql_neon } = await import('./neon');
  const result = await run_sql_neon({
    sql: 'SELECT id, email, full_name as name, password_hash FROM users WHERE email = $1',
    params: [email],
  });
  return result.rows[0];
  */
  
  return users.find(user => user.email === email);
}

async function createUser(email: string, name: string, image?: string) {
  // Replace with actual database query
  // Example with your Neon database:
  /*
  const { run_sql_neon } = await import('./neon');
  const result = await run_sql_neon({
    sql: 'INSERT INTO users (email, full_name, profile_image) VALUES ($1, $2, $3) RETURNING id, email, full_name as name, profile_image as image',
    params: [email, name, image || null],
  });
  return result.rows[0];
  */
  
  const newUser = {
    id: String(users.length + 1),
    email,
    name,
    image,
  };
  users.push({ ...newUser, password: '' });
  return newUser;
}

export const authOptions: NextAuthOptions = {
  providers: [
    // Credentials Provider for email/password login
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await getUserByEmail(credentials.email);
        if (!user) {
          return null;
        }

        // For demo purposes, allow 'password' as password
        // In production, use proper password hashing
        const isPasswordValid = credentials.password === 'password' || 
          (user.password && await bcrypt.compare(credentials.password, user.password));

        if (!isPasswordValid) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
        };
      },
    }),

    // Google OAuth Provider
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET
      ? [
          GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          }),
        ]
      : []),

    // GitHub OAuth Provider
    ...(process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET
      ? [
          GitHubProvider({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET,
          }),
        ]
      : []),
  ],

  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },

    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
      }
      return session;
    },

    async signIn({ user, account, profile }) {
      if (account?.provider === 'google' || account?.provider === 'github') {
        // Check if user exists, create if not
        const existingUser = await getUserByEmail(user.email!);
        if (!existingUser) {
          await createUser(user.email!, user.name!, user.image);
        }
      }
      return true;
    },
  },

  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  secret: process.env.NEXTAUTH_SECRET,

  debug: process.env.NODE_ENV === 'development',
};
