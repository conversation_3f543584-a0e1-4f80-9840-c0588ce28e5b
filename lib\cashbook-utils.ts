// Cashbook Management System Utility Functions

import { 
  Cashbook, 
  Transaction, 
  Category, 
  CashbookCollaborator,
  FinancialSummary,
  TransactionFilters,
  CashbookRole,
  CURRENCY_SYMBOLS,
  SUPPORTED_CURRENCIES
} from '@/types/cashbook';

// Calculate financial summary from transactions
export function calculateFinancialSummary(
  transactions: Transaction[], 
  currency: string = 'USD'
): FinancialSummary {
  const income = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
    
  const expenses = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);
    
  return {
    total_income: income,
    total_expenses: expenses,
    current_balance: income - expenses,
    transaction_count: transactions.length,
    currency,
  };
}

// Filter transactions based on criteria
export function filterTransactions(
  transactions: Transaction[], 
  filters: TransactionFilters
): Transaction[] {
  return transactions.filter(transaction => {
    // Filter by type
    if (filters.type && transaction.type !== filters.type) {
      return false;
    }
    
    // Filter by category
    if (filters.category_id && transaction.category_id !== filters.category_id) {
      return false;
    }
    
    // Filter by date range
    if (filters.date_from) {
      const transactionDate = new Date(transaction.date);
      const fromDate = new Date(filters.date_from);
      if (transactionDate < fromDate) {
        return false;
      }
    }
    
    if (filters.date_to) {
      const transactionDate = new Date(transaction.date);
      const toDate = new Date(filters.date_to);
      if (transactionDate > toDate) {
        return false;
      }
    }
    
    // Filter by search term (description)
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const description = transaction.description?.toLowerCase() || '';
      const categoryName = transaction.category?.name?.toLowerCase() || '';
      
      if (!description.includes(searchTerm) && !categoryName.includes(searchTerm)) {
        return false;
      }
    }
    
    return true;
  });
}

// Sort transactions by various criteria
export function sortTransactions(
  transactions: Transaction[], 
  sortBy: 'date' | 'amount' | 'type' | 'category' = 'date',
  sortOrder: 'asc' | 'desc' = 'desc'
): Transaction[] {
  return [...transactions].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'date':
        comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
        break;
      case 'amount':
        comparison = a.amount - b.amount;
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
      case 'category':
        const aCategoryName = a.category?.name || '';
        const bCategoryName = b.category?.name || '';
        comparison = aCategoryName.localeCompare(bCategoryName);
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
}

// Get user role in a cashbook
export function getUserRole(
  cashbook: Cashbook, 
  userId: string, 
  collaborators: CashbookCollaborator[] = []
): CashbookRole | null {
  // Check if user is the owner
  if (cashbook.owner_id === userId) {
    return 'owner';
  }
  
  // Check collaborator role
  const collaboration = collaborators.find(c => 
    c.cashbook_id === cashbook.id && c.user_id === userId
  );
  
  return collaboration?.role || null;
}

// Check if user has permission for a specific action
export function hasPermission(
  action: 'view' | 'edit' | 'manage' | 'delete',
  userRole: CashbookRole | null
): boolean {
  if (!userRole) return false;
  
  switch (action) {
    case 'view':
      return ['owner', 'editor', 'viewer'].includes(userRole);
    case 'edit':
      return ['owner', 'editor'].includes(userRole);
    case 'manage':
    case 'delete':
      return userRole === 'owner';
    default:
      return false;
  }
}

// Format currency amount with proper symbol
export function formatCurrency(amount: number, currency: string): string {
  const symbol = CURRENCY_SYMBOLS[currency] || currency;
  
  // Handle negative amounts
  const isNegative = amount < 0;
  const absoluteAmount = Math.abs(amount);
  
  const formatted = absoluteAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  const result = `${symbol}${formatted}`;
  return isNegative ? `-${result}` : result;
}

// Get currency symbol
export function getCurrencySymbol(currency: string): string {
  return CURRENCY_SYMBOLS[currency] || currency;
}

// Get currency info
export function getCurrencyInfo(currency: string) {
  return SUPPORTED_CURRENCIES.find(c => c.code === currency);
}

// Group transactions by date
export function groupTransactionsByDate(transactions: Transaction[]): Record<string, Transaction[]> {
  return transactions.reduce((groups, transaction) => {
    const date = transaction.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(transaction);
    return groups;
  }, {} as Record<string, Transaction[]>);
}

// Group transactions by category
export function groupTransactionsByCategory(transactions: Transaction[]): Record<string, Transaction[]> {
  return transactions.reduce((groups, transaction) => {
    const categoryName = transaction.category?.name || 'Uncategorized';
    if (!groups[categoryName]) {
      groups[categoryName] = [];
    }
    groups[categoryName].push(transaction);
    return groups;
  }, {} as Record<string, Transaction[]>);
}

// Calculate category totals
export function calculateCategoryTotals(transactions: Transaction[]): Record<string, number> {
  return transactions.reduce((totals, transaction) => {
    const categoryName = transaction.category?.name || 'Uncategorized';
    if (!totals[categoryName]) {
      totals[categoryName] = 0;
    }
    totals[categoryName] += transaction.amount;
    return totals;
  }, {} as Record<string, number>);
}

// Get date range for transactions
export function getTransactionDateRange(transactions: Transaction[]): { start: string; end: string } | null {
  if (transactions.length === 0) return null;
  
  const dates = transactions.map(t => new Date(t.date));
  const start = new Date(Math.min(...dates.map(d => d.getTime())));
  const end = new Date(Math.max(...dates.map(d => d.getTime())));
  
  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0],
  };
}

// Generate date range for filters
export function generateDateRanges() {
  const today = new Date();
  const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const currentYear = new Date(today.getFullYear(), 0, 1);
  const lastYear = new Date(today.getFullYear() - 1, 0, 1);
  
  return {
    today: {
      start: today.toISOString().split('T')[0],
      end: today.toISOString().split('T')[0],
    },
    thisWeek: {
      start: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: today.toISOString().split('T')[0],
    },
    thisMonth: {
      start: currentMonth.toISOString().split('T')[0],
      end: today.toISOString().split('T')[0],
    },
    lastMonth: {
      start: lastMonth.toISOString().split('T')[0],
      end: new Date(currentMonth.getTime() - 1).toISOString().split('T')[0],
    },
    thisYear: {
      start: currentYear.toISOString().split('T')[0],
      end: today.toISOString().split('T')[0],
    },
    lastYear: {
      start: lastYear.toISOString().split('T')[0],
      end: new Date(currentYear.getTime() - 1).toISOString().split('T')[0],
    },
  };
}

// Validate cashbook access
export function validateCashbookAccess(
  cashbook: Cashbook,
  userId: string,
  collaborators: CashbookCollaborator[],
  requiredPermission: 'view' | 'edit' | 'manage' | 'delete' = 'view'
): boolean {
  const userRole = getUserRole(cashbook, userId, collaborators);
  return hasPermission(requiredPermission, userRole);
}

// Get default categories by type
export function getDefaultCategories(categories: Category[], type: 'income' | 'expense'): Category[] {
  return categories.filter(c => c.type === type && c.is_default);
}

// Get custom categories by type
export function getCustomCategories(categories: Category[], type: 'income' | 'expense', userId: string): Category[] {
  return categories.filter(c => c.type === type && !c.is_default && c.created_by === userId);
}

// Calculate percentage change
export function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

// Format percentage
export function formatPercentage(percentage: number): string {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(1)}%`;
}

// Debounce function for search
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Generate unique ID (fallback if UUID is not available)
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Export utility functions as a group
export const CashbookUtils = {
  calculateFinancialSummary,
  filterTransactions,
  sortTransactions,
  getUserRole,
  hasPermission,
  formatCurrency,
  getCurrencySymbol,
  getCurrencyInfo,
  groupTransactionsByDate,
  groupTransactionsByCategory,
  calculateCategoryTotals,
  getTransactionDateRange,
  generateDateRanges,
  validateCashbookAccess,
  getDefaultCategories,
  getCustomCategories,
  calculatePercentageChange,
  formatPercentage,
  debounce,
  generateId,
};
