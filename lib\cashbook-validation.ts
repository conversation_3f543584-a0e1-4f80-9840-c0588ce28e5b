// Cashbook Management System Validation Utilities

import { 
  CreateCashbookData, 
  UpdateCashbookData, 
  CreateTransactionData, 
  UpdateTransactionData,
  CreateCategoryData,
  InviteCollaboratorData,
  CashbookError,
  CashbookErrorType,
  SUPPORTED_CURRENCIES
} from '@/types/cashbook';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: CashbookError[];
}

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// UUID validation regex
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

// Helper function to create validation error
function createValidationError(field: string, message: string): CashbookError {
  return {
    type: CashbookErrorType.VALIDATION_ERROR,
    message,
    field,
  };
}

// Validate cashbook creation data
export function validateCreateCashbook(data: CreateCashbookData): ValidationResult {
  const errors: CashbookError[] = [];

  // Validate name
  if (!data.name || typeof data.name !== 'string') {
    errors.push(createValidationError('name', 'Cashbook name is required'));
  } else if (data.name.trim().length === 0) {
    errors.push(createValidationError('name', 'Cashbook name cannot be empty'));
  } else if (data.name.length > 100) {
    errors.push(createValidationError('name', 'Cashbook name must be 100 characters or less'));
  }

  // Validate description (optional)
  if (data.description && typeof data.description === 'string' && data.description.length > 500) {
    errors.push(createValidationError('description', 'Description must be 500 characters or less'));
  }

  // Validate currency
  if (!data.currency || typeof data.currency !== 'string') {
    errors.push(createValidationError('currency', 'Currency is required'));
  } else {
    const supportedCurrencyCodes = SUPPORTED_CURRENCIES.map(c => c.code);
    if (!supportedCurrencyCodes.includes(data.currency)) {
      errors.push(createValidationError('currency', `Currency must be one of: ${supportedCurrencyCodes.join(', ')}`));
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validate cashbook update data
export function validateUpdateCashbook(data: UpdateCashbookData): ValidationResult {
  const errors: CashbookError[] = [];

  // Validate name (optional for update)
  if (data.name !== undefined) {
    if (typeof data.name !== 'string') {
      errors.push(createValidationError('name', 'Cashbook name must be a string'));
    } else if (data.name.trim().length === 0) {
      errors.push(createValidationError('name', 'Cashbook name cannot be empty'));
    } else if (data.name.length > 100) {
      errors.push(createValidationError('name', 'Cashbook name must be 100 characters or less'));
    }
  }

  // Validate description (optional)
  if (data.description !== undefined && typeof data.description === 'string' && data.description.length > 500) {
    errors.push(createValidationError('description', 'Description must be 500 characters or less'));
  }

  // Validate currency (optional for update)
  if (data.currency !== undefined) {
    if (typeof data.currency !== 'string') {
      errors.push(createValidationError('currency', 'Currency must be a string'));
    } else {
      const supportedCurrencyCodes = SUPPORTED_CURRENCIES.map(c => c.code);
      if (!supportedCurrencyCodes.includes(data.currency)) {
        errors.push(createValidationError('currency', `Currency must be one of: ${supportedCurrencyCodes.join(', ')}`));
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validate transaction creation data
export function validateCreateTransaction(data: CreateTransactionData): ValidationResult {
  const errors: CashbookError[] = [];

  // Validate cashbook_id
  if (!data.cashbook_id || typeof data.cashbook_id !== 'string') {
    errors.push(createValidationError('cashbook_id', 'Cashbook ID is required'));
  } else if (!UUID_REGEX.test(data.cashbook_id)) {
    errors.push(createValidationError('cashbook_id', 'Cashbook ID must be a valid UUID'));
  }

  // Validate amount
  if (data.amount === undefined || data.amount === null) {
    errors.push(createValidationError('amount', 'Amount is required'));
  } else if (typeof data.amount !== 'number' || isNaN(data.amount)) {
    errors.push(createValidationError('amount', 'Amount must be a valid number'));
  } else if (data.amount <= 0) {
    errors.push(createValidationError('amount', 'Amount must be greater than 0'));
  } else if (data.amount > 999999999.99) {
    errors.push(createValidationError('amount', 'Amount must be less than 1 billion'));
  }

  // Validate type
  if (!data.type || typeof data.type !== 'string') {
    errors.push(createValidationError('type', 'Transaction type is required'));
  } else if (!['income', 'expense'].includes(data.type)) {
    errors.push(createValidationError('type', 'Transaction type must be either "income" or "expense"'));
  }

  // Validate category_id
  if (!data.category_id || typeof data.category_id !== 'string') {
    errors.push(createValidationError('category_id', 'Category ID is required'));
  } else if (!UUID_REGEX.test(data.category_id)) {
    errors.push(createValidationError('category_id', 'Category ID must be a valid UUID'));
  }

  // Validate description (optional)
  if (data.description && typeof data.description === 'string' && data.description.length > 500) {
    errors.push(createValidationError('description', 'Description must be 500 characters or less'));
  }

  // Validate date
  if (!data.date || typeof data.date !== 'string') {
    errors.push(createValidationError('date', 'Date is required'));
  } else {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data.date)) {
      errors.push(createValidationError('date', 'Date must be in YYYY-MM-DD format'));
    } else {
      const parsedDate = new Date(data.date);
      if (isNaN(parsedDate.getTime())) {
        errors.push(createValidationError('date', 'Date must be a valid date'));
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validate transaction update data
export function validateUpdateTransaction(data: UpdateTransactionData): ValidationResult {
  const errors: CashbookError[] = [];

  // Validate amount (optional for update)
  if (data.amount !== undefined) {
    if (typeof data.amount !== 'number' || isNaN(data.amount)) {
      errors.push(createValidationError('amount', 'Amount must be a valid number'));
    } else if (data.amount <= 0) {
      errors.push(createValidationError('amount', 'Amount must be greater than 0'));
    } else if (data.amount > 999999999.99) {
      errors.push(createValidationError('amount', 'Amount must be less than 1 billion'));
    }
  }

  // Validate type (optional for update)
  if (data.type !== undefined) {
    if (typeof data.type !== 'string' || !['income', 'expense'].includes(data.type)) {
      errors.push(createValidationError('type', 'Transaction type must be either "income" or "expense"'));
    }
  }

  // Validate category_id (optional for update)
  if (data.category_id !== undefined) {
    if (typeof data.category_id !== 'string') {
      errors.push(createValidationError('category_id', 'Category ID must be a string'));
    } else if (!UUID_REGEX.test(data.category_id)) {
      errors.push(createValidationError('category_id', 'Category ID must be a valid UUID'));
    }
  }

  // Validate description (optional)
  if (data.description !== undefined && typeof data.description === 'string' && data.description.length > 500) {
    errors.push(createValidationError('description', 'Description must be 500 characters or less'));
  }

  // Validate date (optional for update)
  if (data.date !== undefined) {
    if (typeof data.date !== 'string') {
      errors.push(createValidationError('date', 'Date must be a string'));
    } else {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(data.date)) {
        errors.push(createValidationError('date', 'Date must be in YYYY-MM-DD format'));
      } else {
        const parsedDate = new Date(data.date);
        if (isNaN(parsedDate.getTime())) {
          errors.push(createValidationError('date', 'Date must be a valid date'));
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validate category creation data
export function validateCreateCategory(data: CreateCategoryData): ValidationResult {
  const errors: CashbookError[] = [];

  // Validate name
  if (!data.name || typeof data.name !== 'string') {
    errors.push(createValidationError('name', 'Category name is required'));
  } else if (data.name.trim().length === 0) {
    errors.push(createValidationError('name', 'Category name cannot be empty'));
  } else if (data.name.length > 50) {
    errors.push(createValidationError('name', 'Category name must be 50 characters or less'));
  }

  // Validate type
  if (!data.type || typeof data.type !== 'string') {
    errors.push(createValidationError('type', 'Category type is required'));
  } else if (!['income', 'expense'].includes(data.type)) {
    errors.push(createValidationError('type', 'Category type must be either "income" or "expense"'));
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validate collaborator invitation data
export function validateInviteCollaborator(data: InviteCollaboratorData): ValidationResult {
  const errors: CashbookError[] = [];

  // Validate cashbook_id
  if (!data.cashbook_id || typeof data.cashbook_id !== 'string') {
    errors.push(createValidationError('cashbook_id', 'Cashbook ID is required'));
  } else if (!UUID_REGEX.test(data.cashbook_id)) {
    errors.push(createValidationError('cashbook_id', 'Cashbook ID must be a valid UUID'));
  }

  // Validate email
  if (!data.email || typeof data.email !== 'string') {
    errors.push(createValidationError('email', 'Email is required'));
  } else if (!EMAIL_REGEX.test(data.email)) {
    errors.push(createValidationError('email', 'Email must be a valid email address'));
  }

  // Validate role
  if (!data.role || typeof data.role !== 'string') {
    errors.push(createValidationError('role', 'Role is required'));
  } else if (!['editor', 'viewer'].includes(data.role)) {
    errors.push(createValidationError('role', 'Role must be either "editor" or "viewer"'));
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Validate UUID format
export function isValidUUID(uuid: string): boolean {
  return UUID_REGEX.test(uuid);
}

// Validate email format
export function isValidEmail(email: string): boolean {
  return EMAIL_REGEX.test(email);
}

// Sanitize string input
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

// Format currency amount
export function formatCurrency(amount: number, currency: string): string {
  const currencyInfo = SUPPORTED_CURRENCIES.find(c => c.code === currency);
  const symbol = currencyInfo?.symbol || currency;
  
  return `${symbol}${amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
}

// Parse currency amount from string
export function parseCurrencyAmount(amountString: string): number | null {
  // Remove currency symbols and spaces
  const cleanAmount = amountString.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleanAmount);
  
  if (isNaN(parsed)) {
    return null;
  }
  
  // Round to 2 decimal places
  return Math.round(parsed * 100) / 100;
}

// Validate date range
export function validateDateRange(startDate: string, endDate: string): ValidationResult {
  const errors: CashbookError[] = [];
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime())) {
    errors.push(createValidationError('start_date', 'Start date must be a valid date'));
  }
  
  if (isNaN(end.getTime())) {
    errors.push(createValidationError('end_date', 'End date must be a valid date'));
  }
  
  if (errors.length === 0 && start > end) {
    errors.push(createValidationError('date_range', 'Start date must be before end date'));
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Export validation functions as a group
export const CashbookValidation = {
  validateCreateCashbook,
  validateUpdateCashbook,
  validateCreateTransaction,
  validateUpdateTransaction,
  validateCreateCategory,
  validateInviteCollaborator,
  isValidUUID,
  isValidEmail,
  sanitizeString,
  formatCurrency,
  parseCurrencyAmount,
  validateDateRange,
};
