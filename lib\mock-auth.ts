// Mock Authentication System for Development

export interface MockUser {
  id: string;
  email: string;
  name: string;
  image?: string;
}

export interface MockSession {
  user: MockUser;
  expires: string;
}

// Mock user data with proper UUID (matches the user in database)
const MOCK_USER: MockUser = {
  id: '6f32b7fd-3242-44da-a98e-99165cfcf1f7', // Matches the user created in database
  email: '<EMAIL>',
  name: '<PERSON><PERSON>',
  image: undefined,
};

// Mock session that expires in 30 days
const MOCK_SESSION: MockSession = {
  user: MOCK_USER,
  expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
};

// Mock getServerSession function
export async function getServerSession(): Promise<MockSession | null> {
  // In development, always return a valid session
  if (process.env.NODE_ENV === 'development') {
    return MOCK_SESSION;
  }
  
  // In production, you would implement real session checking
  return null;
}

// Mock authOptions for compatibility
export const authOptions = {
  // Mock configuration
  providers: [],
  callbacks: {},
  pages: {
    signIn: '/auth/signin',
  },
  session: {
    strategy: 'jwt' as const,
  },
  secret: 'mock-secret',
};

// Export for compatibility with NextAuth imports
export { getServerSession as default };
