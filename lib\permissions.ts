// Permission System for Cashbook Management

import { CashbookRole } from '@/types/cashbook';

// Define all possible permissions
export enum Permission {
  // Cashbook permissions
  VIEW_CASHBOOK = 'view_cashbook',
  EDIT_CASHBOOK = 'edit_cashbook',
  DELETE_CASHBOOK = 'delete_cashbook',
  MANAGE_CASHBOOK = 'manage_cashbook',
  
  // Transaction permissions
  VIEW_TRANSACTIONS = 'view_transactions',
  CREATE_TRANSACTION = 'create_transaction',
  EDIT_TRANSACTION = 'edit_transaction',
  DELETE_TRANSACTION = 'delete_transaction',
  
  // Category permissions
  VIEW_CATEGORIES = 'view_categories',
  CREATE_CATEGORY = 'create_category',
  EDIT_CATEGORY = 'edit_category',
  DELETE_CATEGORY = 'delete_category',
  
  // Collaborator permissions
  VIEW_COLLABORATORS = 'view_collaborators',
  INVITE_COLLABORATORS = 'invite_collaborators',
  <PERSON><PERSON><PERSON>_COLLABORATORS = 'manage_collaborators',
  REMOVE_COLLABORATORS = 'remove_collaborators',
  
  // Financial data permissions
  VIEW_FINANCIAL_SUMMARY = 'view_financial_summary',
  EXPORT_DATA = 'export_data',
  
  // Settings permissions
  MANAGE_SETTINGS = 'manage_settings',
}

// Role-based permission matrix
const ROLE_PERMISSIONS: Record<CashbookRole, Permission[]> = {
  owner: [
    // All permissions for owners
    Permission.VIEW_CASHBOOK,
    Permission.EDIT_CASHBOOK,
    Permission.DELETE_CASHBOOK,
    Permission.MANAGE_CASHBOOK,
    Permission.VIEW_TRANSACTIONS,
    Permission.CREATE_TRANSACTION,
    Permission.EDIT_TRANSACTION,
    Permission.DELETE_TRANSACTION,
    Permission.VIEW_CATEGORIES,
    Permission.CREATE_CATEGORY,
    Permission.EDIT_CATEGORY,
    Permission.DELETE_CATEGORY,
    Permission.VIEW_COLLABORATORS,
    Permission.INVITE_COLLABORATORS,
    Permission.MANAGE_COLLABORATORS,
    Permission.REMOVE_COLLABORATORS,
    Permission.VIEW_FINANCIAL_SUMMARY,
    Permission.EXPORT_DATA,
    Permission.MANAGE_SETTINGS,
  ],
  editor: [
    // Editors can view and modify content but not manage the cashbook itself
    Permission.VIEW_CASHBOOK,
    Permission.VIEW_TRANSACTIONS,
    Permission.CREATE_TRANSACTION,
    Permission.EDIT_TRANSACTION,
    Permission.DELETE_TRANSACTION,
    Permission.VIEW_CATEGORIES,
    Permission.CREATE_CATEGORY,
    Permission.VIEW_COLLABORATORS,
    Permission.VIEW_FINANCIAL_SUMMARY,
    Permission.EXPORT_DATA,
  ],
  viewer: [
    // Viewers have read-only access
    Permission.VIEW_CASHBOOK,
    Permission.VIEW_TRANSACTIONS,
    Permission.VIEW_CATEGORIES,
    Permission.VIEW_COLLABORATORS,
    Permission.VIEW_FINANCIAL_SUMMARY,
  ],
};

// Permission checking functions
export function hasPermission(role: CashbookRole | null, permission: Permission): boolean {
  if (!role) return false;
  return ROLE_PERMISSIONS[role]?.includes(permission) || false;
}

export function hasAnyPermission(role: CashbookRole | null, permissions: Permission[]): boolean {
  if (!role) return false;
  return permissions.some(permission => hasPermission(role, permission));
}

export function hasAllPermissions(role: CashbookRole | null, permissions: Permission[]): boolean {
  if (!role) return false;
  return permissions.every(permission => hasPermission(role, permission));
}

// Convenience functions for common permission checks
export const PermissionChecks = {
  // Cashbook management
  canViewCashbook: (role: CashbookRole | null) => hasPermission(role, Permission.VIEW_CASHBOOK),
  canEditCashbook: (role: CashbookRole | null) => hasPermission(role, Permission.EDIT_CASHBOOK),
  canDeleteCashbook: (role: CashbookRole | null) => hasPermission(role, Permission.DELETE_CASHBOOK),
  canManageCashbook: (role: CashbookRole | null) => hasPermission(role, Permission.MANAGE_CASHBOOK),
  
  // Transaction management
  canViewTransactions: (role: CashbookRole | null) => hasPermission(role, Permission.VIEW_TRANSACTIONS),
  canCreateTransaction: (role: CashbookRole | null) => hasPermission(role, Permission.CREATE_TRANSACTION),
  canEditTransaction: (role: CashbookRole | null) => hasPermission(role, Permission.EDIT_TRANSACTION),
  canDeleteTransaction: (role: CashbookRole | null) => hasPermission(role, Permission.DELETE_TRANSACTION),
  canManageTransactions: (role: CashbookRole | null) => hasAnyPermission(role, [
    Permission.CREATE_TRANSACTION,
    Permission.EDIT_TRANSACTION,
    Permission.DELETE_TRANSACTION,
  ]),
  
  // Category management
  canViewCategories: (role: CashbookRole | null) => hasPermission(role, Permission.VIEW_CATEGORIES),
  canCreateCategory: (role: CashbookRole | null) => hasPermission(role, Permission.CREATE_CATEGORY),
  canEditCategory: (role: CashbookRole | null) => hasPermission(role, Permission.EDIT_CATEGORY),
  canDeleteCategory: (role: CashbookRole | null) => hasPermission(role, Permission.DELETE_CATEGORY),
  canManageCategories: (role: CashbookRole | null) => hasAnyPermission(role, [
    Permission.CREATE_CATEGORY,
    Permission.EDIT_CATEGORY,
    Permission.DELETE_CATEGORY,
  ]),
  
  // Collaborator management
  canViewCollaborators: (role: CashbookRole | null) => hasPermission(role, Permission.VIEW_COLLABORATORS),
  canInviteCollaborators: (role: CashbookRole | null) => hasPermission(role, Permission.INVITE_COLLABORATORS),
  canManageCollaborators: (role: CashbookRole | null) => hasPermission(role, Permission.MANAGE_COLLABORATORS),
  canRemoveCollaborators: (role: CashbookRole | null) => hasPermission(role, Permission.REMOVE_COLLABORATORS),
  
  // Financial data
  canViewFinancialSummary: (role: CashbookRole | null) => hasPermission(role, Permission.VIEW_FINANCIAL_SUMMARY),
  canExportData: (role: CashbookRole | null) => hasPermission(role, Permission.EXPORT_DATA),
  
  // Settings
  canManageSettings: (role: CashbookRole | null) => hasPermission(role, Permission.MANAGE_SETTINGS),
  
  // Composite checks
  isOwner: (role: CashbookRole | null) => role === 'owner',
  isEditor: (role: CashbookRole | null) => role === 'editor',
  isViewer: (role: CashbookRole | null) => role === 'viewer',
  canEdit: (role: CashbookRole | null) => role === 'owner' || role === 'editor',
  isReadOnly: (role: CashbookRole | null) => role === 'viewer',
};

// Permission-based UI helpers
export function getVisibleActions(role: CashbookRole | null) {
  return {
    // Cashbook actions
    showEditCashbook: PermissionChecks.canEditCashbook(role),
    showDeleteCashbook: PermissionChecks.canDeleteCashbook(role),
    showManageCollaborators: PermissionChecks.canManageCollaborators(role),
    
    // Transaction actions
    showCreateTransaction: PermissionChecks.canCreateTransaction(role),
    showEditTransaction: PermissionChecks.canEditTransaction(role),
    showDeleteTransaction: PermissionChecks.canDeleteTransaction(role),
    showQuickActions: PermissionChecks.canCreateTransaction(role),
    
    // Category actions
    showCreateCategory: PermissionChecks.canCreateCategory(role),
    showEditCategory: PermissionChecks.canEditCategory(role),
    showDeleteCategory: PermissionChecks.canDeleteCategory(role),
    
    // Collaborator actions
    showInviteCollaborator: PermissionChecks.canInviteCollaborators(role),
    showManageCollaboratorRoles: PermissionChecks.canManageCollaborators(role),
    showRemoveCollaborator: PermissionChecks.canRemoveCollaborators(role),
    
    // Data actions
    showExportData: PermissionChecks.canExportData(role),
    showFinancialSummary: PermissionChecks.canViewFinancialSummary(role),
    
    // Settings
    showSettings: PermissionChecks.canManageSettings(role),
  };
}

// Error messages for permission denials
export const PermissionErrors = {
  INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action',
  OWNER_REQUIRED: 'Only the cashbook owner can perform this action',
  EDITOR_REQUIRED: 'You need editor permissions to perform this action',
  VIEW_ONLY: 'You have view-only access to this cashbook',
  NOT_COLLABORATOR: 'You are not a collaborator on this cashbook',
  INVALID_ROLE: 'Invalid role specified',
};

// Permission validation for API routes
export function validatePermission(
  userRole: CashbookRole | null,
  requiredPermission: Permission,
  throwError: boolean = false
): boolean {
  const hasAccess = hasPermission(userRole, requiredPermission);
  
  if (!hasAccess && throwError) {
    throw new Error(PermissionErrors.INSUFFICIENT_PERMISSIONS);
  }
  
  return hasAccess;
}

// Role hierarchy for permission inheritance
export const ROLE_HIERARCHY: Record<CashbookRole, number> = {
  viewer: 1,
  editor: 2,
  owner: 3,
};

export function hasHigherRole(userRole: CashbookRole | null, targetRole: CashbookRole): boolean {
  if (!userRole) return false;
  return ROLE_HIERARCHY[userRole] > ROLE_HIERARCHY[targetRole];
}

export function hasEqualOrHigherRole(userRole: CashbookRole | null, targetRole: CashbookRole): boolean {
  if (!userRole) return false;
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[targetRole];
}

// Permission context for React components
export interface PermissionContext {
  role: CashbookRole | null;
  permissions: Permission[];
  checks: typeof PermissionChecks;
  actions: ReturnType<typeof getVisibleActions>;
}

export function createPermissionContext(role: CashbookRole | null): PermissionContext {
  return {
    role,
    permissions: role ? ROLE_PERMISSIONS[role] : [],
    checks: PermissionChecks,
    actions: getVisibleActions(role),
  };
}

// Export everything as a group
export const Permissions = {
  Permission,
  ROLE_PERMISSIONS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  PermissionChecks,
  getVisibleActions,
  PermissionErrors,
  validatePermission,
  ROLE_HIERARCHY,
  hasHigherRole,
  hasEqualOrHigherRole,
  createPermissionContext,
};
