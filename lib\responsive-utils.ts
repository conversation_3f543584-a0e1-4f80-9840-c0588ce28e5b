// Responsive Design Utilities for Cashbook Management System

import { useEffect, useState } from 'react';

// Breakpoint definitions (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

// Hook to detect current screen size
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState<{
    width: number;
    height: number;
    breakpoint: Breakpoint | 'xs';
  }>({
    width: 0,
    height: 0,
    breakpoint: 'xs',
  });

  useEffect(() => {
    function updateScreenSize() {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let breakpoint: Breakpoint | 'xs' = 'xs';
      
      if (width >= BREAKPOINTS['2xl']) {
        breakpoint = '2xl';
      } else if (width >= BREAKPOINTS.xl) {
        breakpoint = 'xl';
      } else if (width >= BREAKPOINTS.lg) {
        breakpoint = 'lg';
      } else if (width >= BREAKPOINTS.md) {
        breakpoint = 'md';
      } else if (width >= BREAKPOINTS.sm) {
        breakpoint = 'sm';
      }

      setScreenSize({ width, height, breakpoint });
    }

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  return screenSize;
}

// Hook to detect if screen is mobile
export function useIsMobile() {
  const { breakpoint } = useScreenSize();
  return breakpoint === 'xs' || breakpoint === 'sm';
}

// Hook to detect if screen is tablet
export function useIsTablet() {
  const { breakpoint } = useScreenSize();
  return breakpoint === 'md';
}

// Hook to detect if screen is desktop
export function useIsDesktop() {
  const { breakpoint } = useScreenSize();
  return breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl';
}

// Hook for responsive values
export function useResponsiveValue<T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
  default: T;
}): T {
  const { breakpoint } = useScreenSize();
  
  // Return the most specific value available for current breakpoint
  if (breakpoint === '2xl' && values['2xl'] !== undefined) return values['2xl'];
  if ((breakpoint === 'xl' || breakpoint === '2xl') && values.xl !== undefined) return values.xl;
  if ((breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl') && values.lg !== undefined) return values.lg;
  if ((breakpoint === 'md' || breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl') && values.md !== undefined) return values.md;
  if ((breakpoint === 'sm' || breakpoint === 'md' || breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl') && values.sm !== undefined) return values.sm;
  if (values.xs !== undefined) return values.xs;
  
  return values.default;
}

// Touch detection
export function useIsTouchDevice() {
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    const checkTouch = () => {
      setIsTouch(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore
        navigator.msMaxTouchPoints > 0
      );
    };

    checkTouch();
    window.addEventListener('touchstart', checkTouch, { once: true });
    
    return () => window.removeEventListener('touchstart', checkTouch);
  }, []);

  return isTouch;
}

// Responsive grid columns
export function getResponsiveColumns(
  itemCount: number,
  options: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
    maxColumns?: number;
  } = {}
) {
  const {
    xs = 1,
    sm = 2,
    md = 2,
    lg = 3,
    xl = 4,
    '2xl': xl2 = 4,
    maxColumns = 6,
  } = options;

  return useResponsiveValue({
    xs: Math.min(itemCount, xs, maxColumns),
    sm: Math.min(itemCount, sm, maxColumns),
    md: Math.min(itemCount, md, maxColumns),
    lg: Math.min(itemCount, lg, maxColumns),
    xl: Math.min(itemCount, xl, maxColumns),
    '2xl': Math.min(itemCount, xl2, maxColumns),
    default: Math.min(itemCount, lg, maxColumns),
  });
}

// Responsive spacing
export function getResponsiveSpacing(size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md') {
  const spacingMap = {
    xs: {
      xs: 'space-y-2',
      sm: 'space-y-3',
      md: 'space-y-4',
      lg: 'space-y-4',
      xl: 'space-y-6',
      '2xl': 'space-y-6',
      default: 'space-y-4',
    },
    sm: {
      xs: 'space-y-3',
      sm: 'space-y-4',
      md: 'space-y-6',
      lg: 'space-y-6',
      xl: 'space-y-8',
      '2xl': 'space-y-8',
      default: 'space-y-6',
    },
    md: {
      xs: 'space-y-4',
      sm: 'space-y-6',
      md: 'space-y-8',
      lg: 'space-y-8',
      xl: 'space-y-10',
      '2xl': 'space-y-12',
      default: 'space-y-8',
    },
    lg: {
      xs: 'space-y-6',
      sm: 'space-y-8',
      md: 'space-y-10',
      lg: 'space-y-12',
      xl: 'space-y-16',
      '2xl': 'space-y-20',
      default: 'space-y-12',
    },
    xl: {
      xs: 'space-y-8',
      sm: 'space-y-10',
      md: 'space-y-12',
      lg: 'space-y-16',
      xl: 'space-y-20',
      '2xl': 'space-y-24',
      default: 'space-y-16',
    },
  };

  return useResponsiveValue(spacingMap[size]);
}

// Responsive container padding
export function getResponsiveContainerPadding() {
  return useResponsiveValue({
    xs: 'px-4',
    sm: 'px-6',
    md: 'px-8',
    lg: 'px-8',
    xl: 'px-8',
    '2xl': 'px-8',
    default: 'px-8',
  });
}

// Responsive text sizes
export function getResponsiveTextSize(size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' = 'base') {
  const textSizeMap = {
    xs: {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-sm',
      xl: 'text-sm',
      '2xl': 'text-sm',
      default: 'text-sm',
    },
    sm: {
      xs: 'text-sm',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-base',
      xl: 'text-base',
      '2xl': 'text-base',
      default: 'text-base',
    },
    base: {
      xs: 'text-base',
      sm: 'text-base',
      md: 'text-lg',
      lg: 'text-lg',
      xl: 'text-lg',
      '2xl': 'text-lg',
      default: 'text-lg',
    },
    lg: {
      xs: 'text-lg',
      sm: 'text-lg',
      md: 'text-xl',
      lg: 'text-xl',
      xl: 'text-2xl',
      '2xl': 'text-2xl',
      default: 'text-xl',
    },
    xl: {
      xs: 'text-xl',
      sm: 'text-xl',
      md: 'text-2xl',
      lg: 'text-2xl',
      xl: 'text-3xl',
      '2xl': 'text-3xl',
      default: 'text-2xl',
    },
    '2xl': {
      xs: 'text-2xl',
      sm: 'text-2xl',
      md: 'text-3xl',
      lg: 'text-3xl',
      xl: 'text-4xl',
      '2xl': 'text-4xl',
      default: 'text-3xl',
    },
    '3xl': {
      xs: 'text-3xl',
      sm: 'text-3xl',
      md: 'text-4xl',
      lg: 'text-4xl',
      xl: 'text-5xl',
      '2xl': 'text-6xl',
      default: 'text-4xl',
    },
  };

  return useResponsiveValue(textSizeMap[size]);
}

// Mobile-specific utilities
export const MobileUtils = {
  // Check if device supports hover (desktop)
  supportsHover: () => window.matchMedia('(hover: hover)').matches,
  
  // Get safe area insets for mobile devices
  getSafeAreaInsets: () => ({
    top: parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sat') || '0'),
    right: parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sar') || '0'),
    bottom: parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sab') || '0'),
    left: parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sal') || '0'),
  }),
  
  // Prevent zoom on input focus (iOS)
  preventZoomOnFocus: () => {
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      );
    }
  },
  
  // Enable zoom after input blur
  enableZoom: () => {
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0'
      );
    }
  },
};

// Responsive component props helper
export function getResponsiveProps<T extends Record<string, any>>(
  props: T,
  breakpoint: Breakpoint | 'xs'
): T {
  const responsiveProps = { ...props };
  
  // Remove responsive variants that don't match current breakpoint
  Object.keys(props).forEach(key => {
    if (key.includes(':')) {
      const [bp, propName] = key.split(':');
      if (bp === breakpoint) {
        responsiveProps[propName] = props[key];
      }
      delete responsiveProps[key];
    }
  });
  
  return responsiveProps;
}

// Export all utilities
export const ResponsiveUtils = {
  BREAKPOINTS,
  useScreenSize,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  useResponsiveValue,
  useIsTouchDevice,
  getResponsiveColumns,
  getResponsiveSpacing,
  getResponsiveContainerPadding,
  getResponsiveTextSize,
  getResponsiveProps,
  MobileUtils,
};
