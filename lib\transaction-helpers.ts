// Transaction Helper Functions and Utilities

import { Transaction, Category, CreateTransactionData, UpdateTransactionData } from '@/types/cashbook';

// Transaction validation helpers
export function validateTransactionAmount(amount: string | number): { isValid: boolean; error?: string } {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return { isValid: false, error: 'Amount must be a valid number' };
  }
  
  if (numAmount <= 0) {
    return { isValid: false, error: 'Amount must be greater than 0' };
  }
  
  if (numAmount > 999999999.99) {
    return { isValid: false, error: 'Amount must be less than 1 billion' };
  }
  
  // Check for reasonable decimal places (max 2)
  const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    return { isValid: false, error: 'Amount can have at most 2 decimal places' };
  }
  
  return { isValid: true };
}

export function validateTransactionDate(date: string): { isValid: boolean; error?: string } {
  if (!date) {
    return { isValid: false, error: 'Date is required' };
  }
  
  const parsedDate = new Date(date);
  if (isNaN(parsedDate.getTime())) {
    return { isValid: false, error: 'Please enter a valid date' };
  }
  
  // Check if date is not too far in the future (more than 1 year)
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
  
  if (parsedDate > oneYearFromNow) {
    return { isValid: false, error: 'Date cannot be more than 1 year in the future' };
  }
  
  // Check if date is not too far in the past (more than 10 years)
  const tenYearsAgo = new Date();
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
  
  if (parsedDate < tenYearsAgo) {
    return { isValid: false, error: 'Date cannot be more than 10 years in the past' };
  }
  
  return { isValid: true };
}

export function validateTransactionDescription(description: string): { isValid: boolean; error?: string } {
  if (description && description.length > 500) {
    return { isValid: false, error: 'Description must be 500 characters or less' };
  }
  
  return { isValid: true };
}

export function validateCategorySelection(
  categoryId: string, 
  transactionType: 'income' | 'expense',
  categories: Category[]
): { isValid: boolean; error?: string } {
  if (!categoryId) {
    return { isValid: false, error: 'Category is required' };
  }
  
  const category = categories.find(cat => cat.id === categoryId);
  if (!category) {
    return { isValid: false, error: 'Selected category not found' };
  }
  
  if (category.type !== transactionType) {
    return { isValid: false, error: `Category type (${category.type}) does not match transaction type (${transactionType})` };
  }
  
  return { isValid: true };
}

// Complete transaction validation
export function validateTransactionData(
  data: Partial<CreateTransactionData | UpdateTransactionData>,
  categories: Category[]
): { isValid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  
  // Validate amount
  if (data.amount !== undefined) {
    const amountValidation = validateTransactionAmount(data.amount);
    if (!amountValidation.isValid) {
      errors.amount = amountValidation.error!;
    }
  }
  
  // Validate type
  if (data.type && !['income', 'expense'].includes(data.type)) {
    errors.type = 'Transaction type must be either income or expense';
  }
  
  // Validate category
  if (data.category_id && data.type) {
    const categoryValidation = validateCategorySelection(data.category_id, data.type, categories);
    if (!categoryValidation.isValid) {
      errors.category_id = categoryValidation.error!;
    }
  }
  
  // Validate date
  if (data.date) {
    const dateValidation = validateTransactionDate(data.date);
    if (!dateValidation.isValid) {
      errors.date = dateValidation.error!;
    }
  }
  
  // Validate description
  if (data.description !== undefined) {
    const descriptionValidation = validateTransactionDescription(data.description);
    if (!descriptionValidation.isValid) {
      errors.description = descriptionValidation.error!;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

// Transaction formatting helpers
export function formatTransactionAmount(amount: number, currency: string): string {
  const currencySymbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    NPR: 'Rs.',
    INR: '₹',
    CAD: 'C$',
    AUD: 'A$',
  };
  
  const symbol = currencySymbols[currency] || currency;
  const formatted = amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  return `${symbol}${formatted}`;
}

export function parseAmountInput(input: string): number | null {
  // Remove currency symbols and spaces
  const cleanInput = input.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleanInput);
  
  if (isNaN(parsed)) {
    return null;
  }
  
  // Round to 2 decimal places
  return Math.round(parsed * 100) / 100;
}

// Transaction categorization helpers
export function getDefaultCategoryForType(
  type: 'income' | 'expense',
  categories: Category[]
): Category | null {
  const defaultCategories = categories.filter(cat => cat.type === type && cat.is_default);
  
  if (defaultCategories.length === 0) {
    return null;
  }
  
  // Return the first default category, or a specific one based on type
  if (type === 'income') {
    return defaultCategories.find(cat => cat.name === 'Other Income') || defaultCategories[0];
  } else {
    return defaultCategories.find(cat => cat.name === 'Other Expenses') || defaultCategories[0];
  }
}

export function suggestCategoryFromDescription(
  description: string,
  type: 'income' | 'expense',
  categories: Category[]
): Category | null {
  if (!description) {
    return null;
  }
  
  const descLower = description.toLowerCase();
  const typeCategories = categories.filter(cat => cat.type === type);
  
  // Simple keyword matching for common categories
  const categoryKeywords: Record<string, string[]> = {
    'Food & Dining': ['food', 'restaurant', 'lunch', 'dinner', 'breakfast', 'cafe', 'coffee'],
    'Transportation': ['gas', 'fuel', 'uber', 'taxi', 'bus', 'train', 'parking'],
    'Shopping': ['shopping', 'store', 'amazon', 'purchase', 'buy'],
    'Entertainment': ['movie', 'cinema', 'game', 'entertainment', 'fun'],
    'Groceries': ['grocery', 'groceries', 'supermarket', 'market'],
    'Healthcare': ['doctor', 'hospital', 'medicine', 'pharmacy', 'health'],
    'Salary': ['salary', 'wage', 'paycheck', 'income'],
    'Freelance': ['freelance', 'contract', 'consulting', 'project'],
  };
  
  for (const [categoryName, keywords] of Object.entries(categoryKeywords)) {
    if (keywords.some(keyword => descLower.includes(keyword))) {
      const matchedCategory = typeCategories.find(cat => cat.name === categoryName);
      if (matchedCategory) {
        return matchedCategory;
      }
    }
  }
  
  return null;
}

// Transaction duplicate detection
export function findSimilarTransactions(
  newTransaction: Partial<CreateTransactionData>,
  existingTransactions: Transaction[],
  toleranceHours: number = 24
): Transaction[] {
  if (!newTransaction.amount || !newTransaction.date) {
    return [];
  }
  
  const newDate = new Date(newTransaction.date);
  const tolerance = toleranceHours * 60 * 60 * 1000; // Convert to milliseconds
  
  return existingTransactions.filter(transaction => {
    // Same amount
    if (Math.abs(transaction.amount - newTransaction.amount!) > 0.01) {
      return false;
    }
    
    // Same type
    if (transaction.type !== newTransaction.type) {
      return false;
    }
    
    // Within time tolerance
    const transactionDate = new Date(transaction.date);
    const timeDiff = Math.abs(transactionDate.getTime() - newDate.getTime());
    if (timeDiff > tolerance) {
      return false;
    }
    
    return true;
  });
}

// Transaction statistics helpers
export function calculateTransactionStats(transactions: Transaction[]) {
  const income = transactions.filter(t => t.type === 'income');
  const expenses = transactions.filter(t => t.type === 'expense');
  
  const totalIncome = income.reduce((sum, t) => sum + t.amount, 0);
  const totalExpenses = expenses.reduce((sum, t) => sum + t.amount, 0);
  
  const avgIncome = income.length > 0 ? totalIncome / income.length : 0;
  const avgExpense = expenses.length > 0 ? totalExpenses / expenses.length : 0;
  
  return {
    totalTransactions: transactions.length,
    incomeCount: income.length,
    expenseCount: expenses.length,
    totalIncome,
    totalExpenses,
    netAmount: totalIncome - totalExpenses,
    avgIncome,
    avgExpense,
    avgTransaction: transactions.length > 0 ? (totalIncome + totalExpenses) / transactions.length : 0,
  };
}

// Export all helpers as a group
export const TransactionHelpers = {
  validateTransactionAmount,
  validateTransactionDate,
  validateTransactionDescription,
  validateCategorySelection,
  validateTransactionData,
  formatTransactionAmount,
  parseAmountInput,
  getDefaultCategoryForType,
  suggestCategoryFromDescription,
  findSimilarTransactions,
  calculateTransactionStats,
};
