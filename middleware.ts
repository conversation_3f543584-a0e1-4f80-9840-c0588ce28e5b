import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

// For development with mock authentication
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for API routes, static files, and auth pages
  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/images/") ||
    pathname.startsWith("/auth/") ||
    pathname === "/favicon.ico" ||
    pathname.endsWith(".png") ||
    pathname.endsWith(".jpg") ||
    pathname.endsWith(".jpeg") ||
    pathname.endsWith(".gif") ||
    pathname.endsWith(".svg")
  ) {
    return NextResponse.next()
  }

  // In development mode, allow all requests for now
  if (process.env.NODE_ENV === 'development') {
    return NextResponse.next()
  }

  // For production with NextAuth.js, uncomment this section:
  /*
  import { getToken } from "next-auth/jwt"

  const token = await getToken({ req: request })

  // Check if user is authenticated
  if (!token) {
    return NextResponse.redirect(new URL("/auth/signin", request.url))
  }

  // Role-based route protection for cashbook app
  if (pathname.startsWith("/apps/cashbook")) {
    // Add any specific cashbook access control here
    return NextResponse.next()
  }
  */

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files
     */
    "/((?!api|_next/static|_next/image|favicon.ico|images).*)",
  ],
}
