# Authentication Dependencies Installation

Run these commands to install the required authentication packages:

```bash
# Install NextAuth.js and required dependencies
npm install next-auth
npm install @auth/prisma-adapter  # If using Prisma
npm install @next-auth/prisma-adapter  # Alternative adapter

# Install additional providers (optional)
npm install @auth/google-provider
npm install @auth/github-provider

# Install JWT and crypto dependencies
npm install jsonwebtoken
npm install @types/jsonwebtoken --save-dev

# Install bcryptjs for password hashing (if using credentials)
npm install bcryptjs
npm install @types/bcryptjs --save-dev
```

## Environment Variables

Add these to your `.env.local` file:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-here-make-it-long-and-random

# Database URL (already configured for Neon)
DATABASE_URL=your_neon_database_url

# OAuth Providers (optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# For production, generate a secure secret:
# openssl rand -base64 32
```
