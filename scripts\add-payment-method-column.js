#!/usr/bin/env node

/**
 * Migration script to add payment_method column to transactions table
 */

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function addPaymentMethodColumn() {
  console.log('🔧 Adding payment_method column to transactions table...\n');

  try {
    const sql = neon(process.env.DATABASE_URL);

    // Check if column already exists
    const columnExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'transactions' 
        AND column_name = 'payment_method'
      );
    `;

    if (columnExists[0].exists) {
      console.log('✅ payment_method column already exists');
      return;
    }

    // Add the payment_method column
    await sql`
      ALTER TABLE transactions 
      ADD COLUMN payment_method TEXT DEFAULT 'cash'
    `;

    console.log('✅ Successfully added payment_method column to transactions table');

    // Verify the column was added
    const verification = await sql`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'transactions' 
      AND column_name = 'payment_method'
    `;

    if (verification.length > 0) {
      console.log('📋 Column details:');
      console.log(`   Name: ${verification[0].column_name}`);
      console.log(`   Type: ${verification[0].data_type}`);
      console.log(`   Default: ${verification[0].column_default}`);
      console.log(`   Nullable: ${verification[0].is_nullable}`);
    }

    console.log('\n🎯 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

addPaymentMethodColumn();
