const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function cashbookVerification() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Cashbook Verification: Cash In/Out Button Visibility\n');
    
    // Check mock auth user ID
    const mockUserId = '6f32b7fd-3242-44da-a98e-99165cfcf1f7';
    console.log('👤 Mock Auth User ID:', mockUserId);
    
    // Check if user exists in database
    const user = await sql`
      SELECT id, email, full_name FROM users WHERE id = ${mockUserId}
    `;
    
    if (user.length === 0) {
      console.log('❌ ISSUE: Mock auth user does not exist in database!');
      return;
    }
    
    console.log('✅ Mock auth user exists in database:', user[0].full_name);
    
    // Check Test Cashbook
    const testCashbook = await sql`
      SELECT 
        c.id,
        c.name,
        c.owner_id,
        CASE
          WHEN c.owner_id = ${mockUserId} THEN 'owner'
          ELSE COALESCE(cb.role, 'viewer')
        END as user_role
      FROM cashbooks c
      LEFT JOIN cashbook_collaborators cb ON c.id = cb.cashbook_id AND cb.user_id = ${mockUserId}
      WHERE c.name = 'Test Cashbook'
    `;
    
    if (testCashbook.length === 0) {
      console.log('❌ ISSUE: Test Cashbook not found!');
      return;
    }
    
    const cashbook = testCashbook[0];
    console.log('✅ Test Cashbook found:', cashbook.name);
    console.log('   ID:', cashbook.id);
    console.log('   User Role:', cashbook.user_role);
    
    // Check permissions
    const canEdit = ['owner', 'editor'].includes(cashbook.user_role);
    console.log('   Can Edit:', canEdit ? '✅ YES' : '❌ NO');
    
    if (!canEdit) {
      console.log('❌ ISSUE: User does not have edit permissions!');
      return;
    }
    
    // Check categories
    const incomeCategories = await sql`
      SELECT id, name FROM categories WHERE type = 'income' LIMIT 5
    `;
    
    const expenseCategories = await sql`
      SELECT id, name FROM categories WHERE type = 'expense' LIMIT 5
    `;
    
    console.log('✅ Categories available:');
    console.log('   Income categories:', incomeCategories.length);
    console.log('   Expense categories:', expenseCategories.length);
    
    if (incomeCategories.length === 0 || expenseCategories.length === 0) {
      console.log('❌ ISSUE: Not enough categories available!');
      return;
    }
    
    console.log('\n🎯 FINAL VERDICT:');
    console.log('✅ Mock auth user exists in database');
    console.log('✅ Test Cashbook exists and user is owner');
    console.log('✅ User has edit permissions (canEdit = true)');
    console.log('✅ Income and expense categories are available');
    console.log('✅ All conditions are met for Cash In/Out buttons to be visible!');
    
    console.log('\n🌐 Test URL: http://localhost:3002/apps/cashbook/' + cashbook.id);
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  }
}

cashbookVerification();
