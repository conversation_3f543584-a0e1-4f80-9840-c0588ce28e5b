const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkCashbookSchema() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Checking cashbook table schemas...\n');
    
    const tables = ['cashbooks', 'categories', 'transactions', 'cashbook_collaborators'];
    
    for (const table of tables) {
      console.log(`📋 ${table} table structure:`);
      const columns = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = ${table} AND table_schema = 'public'
        ORDER BY ordinal_position
      `;
      
      if (columns.length === 0) {
        console.log('   ❌ Table does not exist');
      } else {
        columns.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULL)'}`);
        });
      }
      console.log('');
    }
    
    // Check for any existing cashbook data
    console.log('📊 Checking existing data...');
    const cashbookCount = await sql`SELECT COUNT(*) as count FROM cashbooks`;
    const categoryCount = await sql`SELECT COUNT(*) as count FROM categories`;
    const transactionCount = await sql`SELECT COUNT(*) as count FROM transactions`;
    
    console.log(`   - Cashbooks: ${cashbookCount[0].count}`);
    console.log(`   - Categories: ${categoryCount[0].count}`);
    console.log(`   - Transactions: ${transactionCount[0].count}`);
    
    if (cashbookCount[0].count > 0) {
      console.log('\n📋 Existing cashbooks:');
      const cashbooks = await sql`SELECT id, name, description, owner_id, created_at FROM cashbooks LIMIT 5`;
      cashbooks.forEach(cb => {
        console.log(`   - ${cb.name} (ID: ${cb.id}, Owner: ${cb.owner_id})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
  }
}

checkCashbookSchema();
