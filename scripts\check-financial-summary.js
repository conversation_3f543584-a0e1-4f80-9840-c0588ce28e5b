const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkFinancialSummary() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Checking financial_summary table...\n');
    
    // Check if table exists
    const tables = await sql`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'financial_summary'
    `;
    
    if (tables.length === 0) {
      console.log('❌ financial_summary table does not exist');
      console.log('🔧 Creating financial_summary table...');
      
      await sql`
        CREATE TABLE financial_summary (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
          total_income DECIMAL(15,2) DEFAULT 0,
          total_expenses DECIMAL(15,2) DEFAULT 0,
          current_balance DECIMAL(15,2) DEFAULT 0,
          transaction_count INTEGER DEFAULT 0,
          last_transaction_date TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(cashbook_id)
        )
      `;
      
      console.log('✅ financial_summary table created');
    } else {
      console.log('✅ financial_summary table exists');
    }
    
    // Check current user
    const userId = '6f32b7fd-3242-44da-a98e-99165cfcf1f7';
    console.log('\n👤 Checking current user:', userId);
    
    const user = await sql`
      SELECT id, email, full_name FROM users WHERE id = ${userId}
    `;
    
    if (user.length === 0) {
      console.log('❌ Current user does not exist. Creating...');
      
      await sql`
        INSERT INTO users (
          id, email, password_hash, full_name, role, employee_id, department, position,
          employment_type, employment_status, is_active, email_verified,
          created_at, updated_at
        ) VALUES (
          ${userId},
          '<EMAIL>',
          '$2b$10$dummy.hash.for.real.user.development.only',
          'Rajesh Kumar Sharma',
          'staff',
          'EMP001',
          'Development',
          'Software Developer',
          'full_time',
          'active',
          true,
          true,
          NOW(),
          NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
          email = EXCLUDED.email,
          full_name = EXCLUDED.full_name,
          updated_at = NOW()
      `;
      
      console.log('✅ Current user created');
    } else {
      console.log('✅ Current user exists:', user[0].full_name);
    }
    
    // Test the cashbook query
    console.log('\n🧪 Testing cashbook query...');
    
    const testQuery = await sql`
      SELECT DISTINCT
        c.id,
        c.name,
        c.description,
        c.currency,
        c.created_at,
        c.updated_at,
        c.owner_id,
        CASE
          WHEN c.owner_id = ${userId} THEN 'owner'
          ELSE COALESCE(cb.role, 'viewer')
        END as user_role,
        COALESCE(fs.total_income, 0) as total_income,
        COALESCE(fs.total_expenses, 0) as total_expenses,
        COALESCE(fs.current_balance, 0) as current_balance,
        COALESCE(fs.transaction_count, 0) as transaction_count,
        u.full_name as owner_name,
        u.email as owner_email
      FROM cashbooks c
      LEFT JOIN cashbook_collaborators cb ON c.id = cb.cashbook_id AND cb.user_id = ${userId}
      LEFT JOIN financial_summary fs ON c.id = fs.cashbook_id
      LEFT JOIN users u ON c.owner_id = u.id
      WHERE c.owner_id = ${userId} OR cb.user_id = ${userId}
      ORDER BY c.updated_at DESC
    `;
    
    console.log('✅ Cashbook query executed successfully');
    console.log(`📊 Found ${testQuery.length} cashbooks for user`);
    
    console.log('\n🎉 All checks passed! The API should work now.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkFinancialSummary();
