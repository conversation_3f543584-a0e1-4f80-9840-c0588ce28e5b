-- Cashbook Management System Database Schema
-- This script creates the database schema for the cashbook management system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create cashbooks table
CREATE TABLE IF NOT EXISTS cashbooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    currency TEXT NOT NULL DEFAULT 'USD',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    is_default BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
    amount DECIMAL(12,2) NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    category_id UUID NOT NULL REFERENCES categories(id),
    description TEXT,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cashbook collaborators table
CREATE TABLE IF NOT EXISTS cashbook_collaborators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('owner', 'editor', 'viewer')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(cashbook_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cashbooks_owner_id ON cashbooks(owner_id);
CREATE INDEX IF NOT EXISTS idx_transactions_cashbook_id ON transactions(cashbook_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_by ON transactions(created_by);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_cashbook_collaborators_cashbook_id ON cashbook_collaborators(cashbook_id);
CREATE INDEX IF NOT EXISTS idx_cashbook_collaborators_user_id ON cashbook_collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_categories_is_default ON categories(is_default);

-- Enable Row Level Security (RLS) on all tables
ALTER TABLE cashbooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cashbook_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view cashbooks they own or collaborate on" ON cashbooks;
DROP POLICY IF EXISTS "Users can create their own cashbooks" ON cashbooks;
DROP POLICY IF EXISTS "Only owners can update cashbooks" ON cashbooks;
DROP POLICY IF EXISTS "Only owners can delete cashbooks" ON cashbooks;

DROP POLICY IF EXISTS "Users can view transactions in accessible cashbooks" ON transactions;
DROP POLICY IF EXISTS "Users with edit access can create transactions" ON transactions;
DROP POLICY IF EXISTS "Users with edit access can update transactions" ON transactions;
DROP POLICY IF EXISTS "Users with edit access can delete transactions" ON transactions;

DROP POLICY IF EXISTS "Users can view collaborators of accessible cashbooks" ON cashbook_collaborators;
DROP POLICY IF EXISTS "Only owners can manage collaborators" ON cashbook_collaborators;

DROP POLICY IF EXISTS "Users can view default categories and their own custom categories" ON categories;
DROP POLICY IF EXISTS "Users can create custom categories" ON categories;

-- Create RLS policies for cashbooks
CREATE POLICY "Users can view cashbooks they own or collaborate on" ON cashbooks
    FOR SELECT USING (
        owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) OR 
        id IN (
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
        )
    );

CREATE POLICY "Users can create their own cashbooks" ON cashbooks
    FOR INSERT WITH CHECK (owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid));

CREATE POLICY "Only owners can update cashbooks" ON cashbooks
    FOR UPDATE USING (owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid))
    WITH CHECK (owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid));

CREATE POLICY "Only owners can delete cashbooks" ON cashbooks
    FOR DELETE USING (owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid));

-- Create RLS policies for transactions
CREATE POLICY "Users can view transactions in accessible cashbooks" ON transactions
    FOR SELECT USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE 
            owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) OR 
            id IN (
                SELECT cashbook_id FROM cashbook_collaborators 
                WHERE user_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
            )
        )
    );

CREATE POLICY "Users with edit access can create transactions" ON transactions
    FOR INSERT WITH CHECK (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
            UNION
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) AND role IN ('owner', 'editor')
        )
    );

CREATE POLICY "Users with edit access can update transactions" ON transactions
    FOR UPDATE USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
            UNION
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) AND role IN ('owner', 'editor')
        )
    );

CREATE POLICY "Users with edit access can delete transactions" ON transactions
    FOR DELETE USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
            UNION
            SELECT cashbook_id FROM cashbook_collaborators 
            WHERE user_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) AND role IN ('owner', 'editor')
        )
    );

-- Create RLS policies for collaborators
CREATE POLICY "Users can view collaborators of accessible cashbooks" ON cashbook_collaborators
    FOR SELECT USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE 
            owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) OR 
            id IN (
                SELECT cashbook_id FROM cashbook_collaborators 
                WHERE user_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
            )
        )
    );

CREATE POLICY "Only owners can manage collaborators" ON cashbook_collaborators
    FOR ALL USING (
        cashbook_id IN (
            SELECT id FROM cashbooks WHERE owner_id = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
        )
    );

-- Create RLS policies for categories
CREATE POLICY "Users can view default categories and their own custom categories" ON categories
    FOR SELECT USING (
        is_default = TRUE OR created_by = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid)
    );

CREATE POLICY "Users can create custom categories" ON categories
    FOR INSERT WITH CHECK (created_by = (SELECT id FROM users WHERE id = current_setting('app.current_user_id')::uuid) AND is_default = FALSE);

-- Insert default categories
INSERT INTO categories (name, type, is_default) VALUES
-- Income categories
('Salary', 'income', TRUE),
('Freelance', 'income', TRUE),
('Business Income', 'income', TRUE),
('Investment Returns', 'income', TRUE),
('Rental Income', 'income', TRUE),
('Bonus', 'income', TRUE),
('Commission', 'income', TRUE),
('Other Income', 'income', TRUE),

-- Expense categories
('Food & Dining', 'expense', TRUE),
('Transportation', 'expense', TRUE),
('Shopping', 'expense', TRUE),
('Entertainment', 'expense', TRUE),
('Bills & Utilities', 'expense', TRUE),
('Healthcare', 'expense', TRUE),
('Education', 'expense', TRUE),
('Travel', 'expense', TRUE),
('Insurance', 'expense', TRUE),
('Rent', 'expense', TRUE),
('Groceries', 'expense', TRUE),
('Gas & Fuel', 'expense', TRUE),
('Office Supplies', 'expense', TRUE),
('Software & Apps', 'expense', TRUE),
('Other Expenses', 'expense', TRUE)
ON CONFLICT DO NOTHING;

-- Create a function to automatically set updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_cashbooks_updated_at ON cashbooks;
CREATE TRIGGER update_cashbooks_updated_at
    BEFORE UPDATE ON cashbooks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_cashbook_collaborators_updated_at ON cashbook_collaborators;
CREATE TRIGGER update_cashbook_collaborators_updated_at
    BEFORE UPDATE ON cashbook_collaborators
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for cashbook summaries with financial calculations
CREATE OR REPLACE VIEW cashbook_summaries AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.owner_id,
    c.currency,
    c.created_at,
    c.updated_at,
    COALESCE(income.total_income, 0) as total_income,
    COALESCE(expense.total_expenses, 0) as total_expenses,
    COALESCE(income.total_income, 0) - COALESCE(expense.total_expenses, 0) as current_balance,
    COALESCE(trans.transaction_count, 0) as transaction_count
FROM cashbooks c
LEFT JOIN (
    SELECT 
        cashbook_id,
        SUM(amount) as total_income
    FROM transactions 
    WHERE type = 'income'
    GROUP BY cashbook_id
) income ON c.id = income.cashbook_id
LEFT JOIN (
    SELECT 
        cashbook_id,
        SUM(amount) as total_expenses
    FROM transactions 
    WHERE type = 'expense'
    GROUP BY cashbook_id
) expense ON c.id = expense.cashbook_id
LEFT JOIN (
    SELECT 
        cashbook_id,
        COUNT(*) as transaction_count
    FROM transactions
    GROUP BY cashbook_id
) trans ON c.id = trans.cashbook_id;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON cashbooks TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON transactions TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON cashbook_collaborators TO PUBLIC;
GRANT SELECT, INSERT ON categories TO PUBLIC;
GRANT SELECT ON cashbook_summaries TO PUBLIC;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO PUBLIC;

COMMENT ON TABLE cashbooks IS 'Stores cashbook information with owner and basic details';
COMMENT ON TABLE transactions IS 'Stores individual transactions within cashbooks';
COMMENT ON TABLE categories IS 'Stores transaction categories (both default and custom)';
COMMENT ON TABLE cashbook_collaborators IS 'Manages user access and permissions for shared cashbooks';
COMMENT ON VIEW cashbook_summaries IS 'Provides financial summaries for each cashbook including totals and balances';
