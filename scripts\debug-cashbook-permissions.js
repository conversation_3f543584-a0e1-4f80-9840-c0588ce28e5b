const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function debugCashbookPermissions() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Debugging Cashbook Permissions and Data...\n');
    
    // Get the current user
    const userId = '6f32b7fd-3242-44da-a98e-99165cfcf1f7';
    
    console.log('👤 Current User ID:', userId);
    
    // Check if user exists
    const user = await sql`
      SELECT id, email, full_name FROM users WHERE id = ${userId}
    `;
    
    if (user.length === 0) {
      console.log('❌ User not found in database!');
      return;
    }
    
    console.log('✅ User found:', user[0].full_name, '(' + user[0].email + ')');
    
    // Get all cashbooks for this user
    console.log('\n📚 Cashbooks for this user:');
    
    const cashbooks = await sql`
      SELECT DISTINCT
        c.id,
        c.name,
        c.description,
        c.currency,
        c.created_at,
        c.updated_at,
        c.owner_id,
        CASE
          WHEN c.owner_id = ${userId} THEN 'owner'
          ELSE COALESCE(cb.role, 'viewer')
        END as user_role,
        u.full_name as owner_name,
        u.email as owner_email
      FROM cashbooks c
      LEFT JOIN cashbook_collaborators cb ON c.id = cb.cashbook_id AND cb.user_id = ${userId}
      LEFT JOIN users u ON c.owner_id = u.id
      WHERE c.owner_id = ${userId} OR cb.user_id = ${userId}
      ORDER BY c.updated_at DESC
    `;
    
    if (cashbooks.length === 0) {
      console.log('❌ No cashbooks found for this user!');
      return;
    }
    
    cashbooks.forEach((cashbook, index) => {
      console.log(`${index + 1}. ${cashbook.name}`);
      console.log(`   ID: ${cashbook.id}`);
      console.log(`   Role: ${cashbook.user_role}`);
      console.log(`   Owner: ${cashbook.owner_name} (${cashbook.owner_email})`);
      console.log(`   Currency: ${cashbook.currency}`);
      console.log('');
    });
    
    // Check categories
    console.log('📂 Available Categories:');
    
    const categories = await sql`
      SELECT id, name, type, is_default FROM categories ORDER BY type, name
    `;
    
    const incomeCategories = categories.filter(c => c.type === 'income');
    const expenseCategories = categories.filter(c => c.type === 'expense');
    
    console.log(`Income categories (${incomeCategories.length}):`);
    incomeCategories.forEach(cat => {
      console.log(`   - ${cat.name} ${cat.is_default ? '(default)' : ''}`);
    });
    
    console.log(`\nExpense categories (${expenseCategories.length}):`);
    expenseCategories.forEach(cat => {
      console.log(`   - ${cat.name} ${cat.is_default ? '(default)' : ''}`);
    });
    
    // Check financial summaries
    console.log('\n💰 Financial Summaries:');
    
    for (const cashbook of cashbooks) {
      const summary = await sql`
        SELECT 
          total_income,
          total_expenses,
          current_balance,
          transaction_count
        FROM financial_summary 
        WHERE cashbook_id = ${cashbook.id}
      `;
      
      console.log(`${cashbook.name}:`);
      if (summary.length > 0) {
        console.log(`   Income: $${summary[0].total_income}`);
        console.log(`   Expenses: $${summary[0].total_expenses}`);
        console.log(`   Balance: $${summary[0].current_balance}`);
        console.log(`   Transactions: ${summary[0].transaction_count}`);
      } else {
        console.log('   No financial summary found');
      }
      console.log('');
    }
    
    // Check permissions for each cashbook
    console.log('🔐 Permission Analysis:');
    
    cashbooks.forEach(cashbook => {
      const role = cashbook.user_role;
      const canView = ['owner', 'editor', 'viewer'].includes(role);
      const canEdit = ['owner', 'editor'].includes(role);
      const canManage = role === 'owner';
      
      console.log(`${cashbook.name} (Role: ${role}):`);
      console.log(`   Can View: ${canView ? '✅' : '❌'}`);
      console.log(`   Can Edit: ${canEdit ? '✅' : '❌'} ${canEdit ? '(Cash In/Out buttons should show)' : '(Cash In/Out buttons hidden)'}`);
      console.log(`   Can Manage: ${canManage ? '✅' : '❌'}`);
      console.log('');
    });
    
    // Test specific cashbook (Test Cashbook)
    const testCashbook = cashbooks.find(c => c.name === 'Test Cashbook');
    if (testCashbook) {
      console.log('🧪 Test Cashbook Analysis:');
      console.log(`   ID: ${testCashbook.id}`);
      console.log(`   User Role: ${testCashbook.user_role}`);
      console.log(`   Can Edit: ${['owner', 'editor'].includes(testCashbook.user_role) ? '✅ YES' : '❌ NO'}`);
      console.log(`   Categories Available: Income(${incomeCategories.length}), Expense(${expenseCategories.length})`);
      
      if (['owner', 'editor'].includes(testCashbook.user_role) && incomeCategories.length > 0 && expenseCategories.length > 0) {
        console.log('   🎯 Cash In/Out buttons SHOULD be visible!');
      } else {
        console.log('   ⚠️  Cash In/Out buttons will be hidden due to:');
        if (!['owner', 'editor'].includes(testCashbook.user_role)) {
          console.log('      - Insufficient permissions (need owner or editor role)');
        }
        if (incomeCategories.length === 0) {
          console.log('      - No income categories available');
        }
        if (expenseCategories.length === 0) {
          console.log('      - No expense categories available');
        }
      }
    }
    
    console.log('\n🌐 Next Steps:');
    console.log('1. Open browser to: http://localhost:3002/apps/cashbook');
    console.log('2. Navigate to the Test Cashbook');
    console.log('3. Check browser console for any JavaScript errors');
    console.log('4. Look for Cash In/Out buttons after Financial Overview section');
    
  } catch (error) {
    console.error('❌ Error debugging cashbook permissions:', error);
  }
}

debugCashbookPermissions();
