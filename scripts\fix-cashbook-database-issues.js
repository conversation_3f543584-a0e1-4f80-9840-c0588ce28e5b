const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function fixCashbookDatabaseIssues() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔧 Fixing Cashbook Database Issues...\n');
    
    // Step 1: Create the mock user if it doesn't exist
    console.log('👤 Creating/updating mock user...');
    const mockUserId = '550e8400-e29b-41d4-a716-446655440000';
    
    // Check if mock user exists
    const existingUser = await sql`
      SELECT id FROM users WHERE id = ${mockUserId}
    `;
    
    if (existingUser.length === 0) {
      // Create the mock user with a dummy password hash
      await sql`
        INSERT INTO users (
          id, email, password_hash, full_name, role, employee_id, department, position,
          employment_type, employment_status, is_active, email_verified,
          created_at, updated_at
        ) VALUES (
          ${mockUserId},
          '<EMAIL>',
          '$2b$10$dummy.hash.for.mock.user.development.only',
          'Demo User',
          'staff',
          'DEMO001',
          'Development',
          'Demo User',
          'full_time',
          'active',
          true,
          true,
          NOW(),
          NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
          email = EXCLUDED.email,
          full_name = EXCLUDED.full_name,
          updated_at = NOW()
      `;
      console.log('✅ Mock user created successfully');
    } else {
      console.log('✅ Mock user already exists');
    }
    
    // Step 2: Create financial_summary table
    console.log('\n💰 Creating financial_summary table...');
    
    await sql`
      CREATE TABLE IF NOT EXISTS financial_summary (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
        total_income DECIMAL(15,2) DEFAULT 0,
        total_expenses DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        transaction_count INTEGER DEFAULT 0,
        last_transaction_date TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(cashbook_id)
      )
    `;
    
    console.log('✅ financial_summary table created');
    
    // Step 3: Create trigger function to update financial_summary
    console.log('\n🔄 Creating trigger function for automatic financial summary updates...');
    
    await sql`
      CREATE OR REPLACE FUNCTION update_financial_summary()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Update or insert financial summary for the cashbook
        INSERT INTO financial_summary (
          cashbook_id,
          total_income,
          total_expenses,
          current_balance,
          transaction_count,
          last_transaction_date,
          updated_at
        )
        SELECT 
          c.id as cashbook_id,
          COALESCE(income.total_income, 0) as total_income,
          COALESCE(expense.total_expenses, 0) as total_expenses,
          COALESCE(income.total_income, 0) - COALESCE(expense.total_expenses, 0) as current_balance,
          COALESCE(trans.transaction_count, 0) as transaction_count,
          trans.last_transaction_date,
          NOW()
        FROM cashbooks c
        LEFT JOIN (
          SELECT 
            cashbook_id,
            SUM(amount) as total_income
          FROM transactions 
          WHERE type = 'income'
          GROUP BY cashbook_id
        ) income ON c.id = income.cashbook_id
        LEFT JOIN (
          SELECT 
            cashbook_id,
            SUM(amount) as total_expenses
          FROM transactions 
          WHERE type = 'expense'
          GROUP BY cashbook_id
        ) expense ON c.id = expense.cashbook_id
        LEFT JOIN (
          SELECT 
            cashbook_id,
            COUNT(*) as transaction_count,
            MAX(created_at) as last_transaction_date
          FROM transactions
          GROUP BY cashbook_id
        ) trans ON c.id = trans.cashbook_id
        WHERE c.id = COALESCE(NEW.cashbook_id, OLD.cashbook_id)
        ON CONFLICT (cashbook_id) DO UPDATE SET
          total_income = EXCLUDED.total_income,
          total_expenses = EXCLUDED.total_expenses,
          current_balance = EXCLUDED.current_balance,
          transaction_count = EXCLUDED.transaction_count,
          last_transaction_date = EXCLUDED.last_transaction_date,
          updated_at = NOW();
        
        RETURN COALESCE(NEW, OLD);
      END;
      $$ LANGUAGE plpgsql;
    `;
    
    console.log('✅ Trigger function created');
    
    // Step 4: Create triggers on transactions table
    console.log('\n⚡ Creating triggers on transactions table...');

    // Drop existing triggers first
    await sql`DROP TRIGGER IF EXISTS update_financial_summary_on_insert ON transactions`;
    await sql`DROP TRIGGER IF EXISTS update_financial_summary_on_update ON transactions`;
    await sql`DROP TRIGGER IF EXISTS update_financial_summary_on_delete ON transactions`;

    // Create new triggers
    await sql`
      CREATE TRIGGER update_financial_summary_on_insert
        AFTER INSERT ON transactions
        FOR EACH ROW
        EXECUTE FUNCTION update_financial_summary()
    `;

    await sql`
      CREATE TRIGGER update_financial_summary_on_update
        AFTER UPDATE ON transactions
        FOR EACH ROW
        EXECUTE FUNCTION update_financial_summary()
    `;

    await sql`
      CREATE TRIGGER update_financial_summary_on_delete
        AFTER DELETE ON transactions
        FOR EACH ROW
        EXECUTE FUNCTION update_financial_summary()
    `;
    
    console.log('✅ Triggers created on transactions table');
    
    // Step 5: Initialize financial_summary for existing cashbooks
    console.log('\n📊 Initializing financial summaries for existing cashbooks...');
    
    const existingCashbooks = await sql`
      SELECT id FROM cashbooks
    `;
    
    if (existingCashbooks.length > 0) {
      await sql`
        INSERT INTO financial_summary (
          cashbook_id,
          total_income,
          total_expenses,
          current_balance,
          transaction_count,
          last_transaction_date
        )
        SELECT 
          c.id as cashbook_id,
          COALESCE(income.total_income, 0) as total_income,
          COALESCE(expense.total_expenses, 0) as total_expenses,
          COALESCE(income.total_income, 0) - COALESCE(expense.total_expenses, 0) as current_balance,
          COALESCE(trans.transaction_count, 0) as transaction_count,
          trans.last_transaction_date
        FROM cashbooks c
        LEFT JOIN (
          SELECT 
            cashbook_id,
            SUM(amount) as total_income
          FROM transactions 
          WHERE type = 'income'
          GROUP BY cashbook_id
        ) income ON c.id = income.cashbook_id
        LEFT JOIN (
          SELECT 
            cashbook_id,
            SUM(amount) as total_expenses
          FROM transactions 
          WHERE type = 'expense'
          GROUP BY cashbook_id
        ) expense ON c.id = expense.cashbook_id
        LEFT JOIN (
          SELECT 
            cashbook_id,
            COUNT(*) as transaction_count,
            MAX(created_at) as last_transaction_date
          FROM transactions
          GROUP BY cashbook_id
        ) trans ON c.id = trans.cashbook_id
        ON CONFLICT (cashbook_id) DO NOTHING
      `;
      
      console.log(`✅ Initialized financial summaries for ${existingCashbooks.length} existing cashbooks`);
    } else {
      console.log('✅ No existing cashbooks found - ready for new cashbooks');
    }
    
    // Step 6: Verify the setup
    console.log('\n🔍 Verifying setup...');
    
    const userCheck = await sql`
      SELECT id, email, full_name FROM users WHERE id = ${mockUserId}
    `;
    
    const tableCheck = await sql`
      SELECT table_name FROM information_schema.tables 
      WHERE table_name = 'financial_summary' AND table_schema = 'public'
    `;
    
    const triggerCheck = await sql`
      SELECT trigger_name FROM information_schema.triggers 
      WHERE event_object_table = 'transactions' 
      AND trigger_name LIKE 'update_financial_summary%'
    `;
    
    console.log(`✅ Mock user exists: ${userCheck.length > 0 ? 'Yes' : 'No'}`);
    console.log(`✅ financial_summary table exists: ${tableCheck.length > 0 ? 'Yes' : 'No'}`);
    console.log(`✅ Triggers created: ${triggerCheck.length} triggers found`);
    
    if (userCheck.length > 0) {
      console.log(`   - User: ${userCheck[0].full_name} (${userCheck[0].email})`);
    }
    
    console.log('\n🎉 Cashbook database issues fixed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('   1. ✅ Mock user created with proper UUID');
    console.log('   2. ✅ financial_summary table created');
    console.log('   3. ✅ Automatic update triggers installed');
    console.log('   4. ✅ Existing cashbooks initialized');
    
  } catch (error) {
    console.error('❌ Error fixing database issues:', error);
    throw error;
  }
}

fixCashbookDatabaseIssues();
