const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function quickTest() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('Testing transaction creation...');
    
    // Get sample data
    const cashbook = await sql`SELECT id FROM cashbooks LIMIT 1`;
    const category = await sql`SELECT id FROM categories WHERE type = 'income' LIMIT 1`;
    
    if (cashbook.length > 0 && category.length > 0) {
      console.log('Sample data found, creating test transaction...');
      
      // Test transaction creation
      const result = await sql`
        INSERT INTO transactions (
          cashbook_id, amount, type, category_id, description, date, payment_method, created_by
        ) VALUES (
          ${cashbook[0].id}, 50.00, 'income', ${category[0].id}, 'Test transaction', 
          ${new Date().toISOString().split('T')[0]}, 'cash', '6f32b7fd-3242-44da-a98e-99165cfcf1f7'
        ) RETURNING id, amount, payment_method
      `;
      
      console.log('✅ Transaction created:', result[0]);
      
      // Clean up
      await sql`DELETE FROM transactions WHERE id = ${result[0].id}`;
      console.log('✅ Test transaction cleaned up');
      console.log('✅ Database is ready for transaction creation!');
    } else {
      console.log('❌ No sample data found');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

quickTest();
