#!/usr/bin/env node

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function removeDuplicateCategories() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🔍 Checking for duplicate categories...');
    
    // Check current categories
    const allCategories = await sql`SELECT id, name, type, created_at FROM categories ORDER BY name, type, created_at`;
    console.log('Total categories before cleanup:', allCategories.length);
    
    // Find duplicates by name and type
    const duplicates = {};
    allCategories.forEach(cat => {
      const key = `${cat.name}-${cat.type}`;
      if (!duplicates[key]) {
        duplicates[key] = [];
      }
      duplicates[key].push(cat);
    });
    
    let duplicateCount = 0;
    let toDelete = [];
    
    Object.keys(duplicates).forEach(key => {
      const cats = duplicates[key];
      if (cats.length > 1) {
        console.log(`Found ${cats.length} duplicates for: ${key}`);
        duplicateCount += cats.length - 1;
        
        // Keep the first one (oldest), delete the rest
        for (let i = 1; i < cats.length; i++) {
          toDelete.push(cats[i].id);
        }
      }
    });
    
    console.log(`Found ${duplicateCount} duplicate categories to remove`);
    
    if (toDelete.length > 0) {
      console.log('🗑️ Removing duplicate categories...');
      
      for (const id of toDelete) {
        await sql`DELETE FROM categories WHERE id = ${id}`;
      }
      
      console.log(`✅ Removed ${toDelete.length} duplicate categories`);
      
      // Verify cleanup
      const finalCategories = await sql`SELECT COUNT(*) as count FROM categories`;
      const incomeCount = await sql`SELECT COUNT(*) as count FROM categories WHERE type = 'income'`;
      const expenseCount = await sql`SELECT COUNT(*) as count FROM categories WHERE type = 'expense'`;
      
      console.log(`Final count: ${finalCategories[0].count} total (${incomeCount[0].count} income, ${expenseCount[0].count} expense)`);
      
      // Show remaining categories
      console.log('\n📋 Remaining categories:');
      const remainingCategories = await sql`SELECT name, type FROM categories ORDER BY type, name`;
      
      const incomeCategories = remainingCategories.filter(c => c.type === 'income');
      const expenseCategories = remainingCategories.filter(c => c.type === 'expense');
      
      console.log('\n💰 Income Categories:');
      incomeCategories.forEach(cat => console.log(`  - ${cat.name}`));
      
      console.log('\n💸 Expense Categories:');
      expenseCategories.forEach(cat => console.log(`  - ${cat.name}`));
      
    } else {
      console.log('✅ No duplicates found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

removeDuplicateCategories();
