#!/usr/bin/env node

/**
 * Cashbook Management System Database Setup Script
 * This script creates the database schema for the cashbook management system
 */

const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

async function setupCashbookDatabase() {
  console.log('🏦 Setting up Cashbook Management System database schema...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    console.log('📝 Please update your .env.local file with your Neon connection string');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');
    
    // Read the SQL schema file
    const schemaPath = path.join(__dirname, 'create-cashbook-schema.sql');
    console.log('📄 Reading schema file:', schemaPath);
    
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ Schema file not found:', schemaPath);
      process.exit(1);
    }
    
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    console.log('✅ Schema file loaded successfully\n');
    
    // Execute the schema
    console.log('🔄 Creating cashbook management schema...');
    console.log('   - Creating tables (cashbooks, transactions, categories, collaborators)');
    console.log('   - Setting up Row Level Security (RLS) policies');
    console.log('   - Creating indexes for performance');
    console.log('   - Inserting default categories');
    console.log('   - Creating views and triggers\n');
    
    // Execute the entire schema as one transaction
    try {
      await sql.unsafe(schemaSQL);
      console.log('✅ Schema executed successfully');
    } catch (error) {
      console.error('❌ Error executing schema:', error.message);

      // Try executing individual statements if the bulk execution fails
      console.log('🔄 Attempting to execute statements individually...');

      const statements = schemaSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        try {
          await sql.unsafe(statement);
          successCount++;
        } catch (stmtError) {
          // Some errors are expected (like DROP POLICY IF EXISTS on non-existent policies)
          if (!stmtError.message.includes('does not exist') &&
              !stmtError.message.includes('already exists') &&
              !stmtError.message.includes('duplicate key value')) {
            console.warn(`⚠️  Warning executing statement ${i + 1}: ${stmtError.message}`);
            errorCount++;
          }
        }
      }

      console.log(`📊 Individual execution results: ${successCount} success, ${errorCount} warnings`);
    }

    console.log(`✅ Schema creation completed!`);
    
    // Verify the tables were created
    console.log('\n🔍 Verifying created tables...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('cashbooks', 'transactions', 'categories', 'cashbook_collaborators')
      ORDER BY table_name
    `;
    
    console.log('📋 Cashbook tables created:');
    tables.forEach(table => {
      console.log(`   ✓ ${table.table_name}`);
    });
    
    // Check default categories
    console.log('\n🔍 Verifying default categories...');
    const categories = await sql`
      SELECT type, COUNT(*) as count 
      FROM categories 
      WHERE is_default = true 
      GROUP BY type 
      ORDER BY type
    `;
    
    console.log('📋 Default categories:');
    categories.forEach(cat => {
      console.log(`   ✓ ${cat.type}: ${cat.count} categories`);
    });
    
    // Check RLS policies
    console.log('\n🔍 Verifying Row Level Security policies...');
    const policies = await sql`
      SELECT schemaname, tablename, policyname 
      FROM pg_policies 
      WHERE schemaname = 'public' 
      AND tablename IN ('cashbooks', 'transactions', 'categories', 'cashbook_collaborators')
      ORDER BY tablename, policyname
    `;
    
    console.log('🔒 RLS policies created:');
    let currentTable = '';
    policies.forEach(policy => {
      if (policy.tablename !== currentTable) {
        currentTable = policy.tablename;
        console.log(`   📋 ${policy.tablename}:`);
      }
      console.log(`      ✓ ${policy.policyname}`);
    });
    
    // Check indexes
    console.log('\n🔍 Verifying indexes...');
    const indexes = await sql`
      SELECT indexname, tablename 
      FROM pg_indexes 
      WHERE schemaname = 'public' 
      AND tablename IN ('cashbooks', 'transactions', 'categories', 'cashbook_collaborators')
      AND indexname LIKE 'idx_%'
      ORDER BY tablename, indexname
    `;
    
    console.log('⚡ Performance indexes created:');
    let currentIndexTable = '';
    indexes.forEach(index => {
      if (index.tablename !== currentIndexTable) {
        currentIndexTable = index.tablename;
        console.log(`   📋 ${index.tablename}:`);
      }
      console.log(`      ✓ ${index.indexname}`);
    });
    
    // Check views
    console.log('\n🔍 Verifying views...');
    const views = await sql`
      SELECT table_name 
      FROM information_schema.views 
      WHERE table_schema = 'public' 
      AND table_name = 'cashbook_summaries'
    `;
    
    if (views.length > 0) {
      console.log('👁️  Views created:');
      views.forEach(view => {
        console.log(`   ✓ ${view.table_name}`);
      });
    }
    
    console.log('\n🎉 Cashbook Management System database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Create TypeScript interfaces and data models');
    console.log('2. Implement CashbookContext provider');
    console.log('3. Build UI components and screens');
    console.log('4. Create API routes for CRUD operations');
    console.log('5. Add comprehensive testing');
    
  } catch (error) {
    console.error('❌ Error setting up cashbook database:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the setup
setupCashbookDatabase();
