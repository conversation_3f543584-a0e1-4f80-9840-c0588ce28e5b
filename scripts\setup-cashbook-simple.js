#!/usr/bin/env node

/**
 * Simple Cashbook Database Setup Script
 * Creates the basic cashbook schema without complex RLS policies
 */

const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

async function setupCashbookSimple() {
  console.log('🏦 Setting up Cashbook Management System (Simple Version)...\n');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    // Test connection
    console.log('🔄 Testing database connection...');
    await sql`SELECT 1`;
    console.log('✅ Database connection successful!\n');

    // Enable UUID extension
    console.log('🔄 Enabling UUID extension...');
    await sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    console.log('✅ UUID extension enabled\n');

    // Create tables step by step
    console.log('🔄 Creating cashbooks table...');
    await sql`
      CREATE TABLE IF NOT EXISTS cashbooks (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          description TEXT,
          owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          currency TEXT NOT NULL DEFAULT 'USD',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Cashbooks table created');
    
    console.log('🔄 Creating categories table...');
    await sql`
      CREATE TABLE IF NOT EXISTS categories (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
          is_default BOOLEAN DEFAULT FALSE,
          created_by UUID REFERENCES users(id) ON DELETE SET NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Categories table created');
    
    console.log('🔄 Creating transactions table...');
    await sql`
      CREATE TABLE IF NOT EXISTS transactions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
          amount DECIMAL(12,2) NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
          category_id UUID NOT NULL REFERENCES categories(id),
          description TEXT,
          date DATE NOT NULL DEFAULT CURRENT_DATE,
          created_by UUID NOT NULL REFERENCES users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `;
    console.log('✅ Transactions table created');
    
    console.log('🔄 Creating cashbook_collaborators table...');
    await sql`
      CREATE TABLE IF NOT EXISTS cashbook_collaborators (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          cashbook_id UUID NOT NULL REFERENCES cashbooks(id) ON DELETE CASCADE,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          role TEXT NOT NULL CHECK (role IN ('owner', 'editor', 'viewer')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(cashbook_id, user_id)
      )
    `;
    console.log('✅ Cashbook collaborators table created');
    
    // Create indexes
    console.log('🔄 Creating performance indexes...');
    await sql`CREATE INDEX IF NOT EXISTS idx_cashbooks_owner_id ON cashbooks(owner_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_transactions_cashbook_id ON transactions(cashbook_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_transactions_created_by ON transactions(created_by)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_cashbook_collaborators_cashbook_id ON cashbook_collaborators(cashbook_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_cashbook_collaborators_user_id ON cashbook_collaborators(user_id)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_categories_is_default ON categories(is_default)`;
    console.log('✅ Indexes created');
    
    // Insert default categories
    console.log('🔄 Inserting default categories...');
    
    // Income categories
    const incomeCategories = [
      'Salary', 'Freelance', 'Business Income', 'Investment Returns',
      'Rental Income', 'Bonus', 'Commission', 'Other Income'
    ];
    
    for (const category of incomeCategories) {
      await sql`
        INSERT INTO categories (name, type, is_default) 
        VALUES (${category}, 'income', TRUE)
        ON CONFLICT DO NOTHING
      `;
    }
    
    // Expense categories
    const expenseCategories = [
      'Food & Dining', 'Transportation', 'Shopping', 'Entertainment',
      'Bills & Utilities', 'Healthcare', 'Education', 'Travel',
      'Insurance', 'Rent', 'Groceries', 'Gas & Fuel',
      'Office Supplies', 'Software & Apps', 'Other Expenses'
    ];
    
    for (const category of expenseCategories) {
      await sql`
        INSERT INTO categories (name, type, is_default) 
        VALUES (${category}, 'expense', TRUE)
        ON CONFLICT DO NOTHING
      `;
    }
    
    console.log('✅ Default categories inserted');
    
    // Create update trigger function
    console.log('🔄 Creating update trigger function...');
    await sql.unsafe(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    // Create triggers
    await sql.unsafe(`DROP TRIGGER IF EXISTS update_cashbooks_updated_at ON cashbooks`);
    await sql.unsafe(`
      CREATE TRIGGER update_cashbooks_updated_at
          BEFORE UPDATE ON cashbooks
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column()
    `);

    await sql.unsafe(`DROP TRIGGER IF EXISTS update_cashbook_collaborators_updated_at ON cashbook_collaborators`);
    await sql.unsafe(`
      CREATE TRIGGER update_cashbook_collaborators_updated_at
          BEFORE UPDATE ON cashbook_collaborators
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column()
    `);
    
    console.log('✅ Triggers created');
    
    // Create view
    console.log('🔄 Creating cashbook summaries view...');
    await sql.unsafe(`
      CREATE OR REPLACE VIEW cashbook_summaries AS
      SELECT
          c.id,
          c.name,
          c.description,
          c.owner_id,
          c.currency,
          c.created_at,
          c.updated_at,
          COALESCE(income.total_income, 0) as total_income,
          COALESCE(expense.total_expenses, 0) as total_expenses,
          COALESCE(income.total_income, 0) - COALESCE(expense.total_expenses, 0) as current_balance,
          COALESCE(trans.transaction_count, 0) as transaction_count
      FROM cashbooks c
      LEFT JOIN (
          SELECT
              cashbook_id,
              SUM(amount) as total_income
          FROM transactions
          WHERE type = 'income'
          GROUP BY cashbook_id
      ) income ON c.id = income.cashbook_id
      LEFT JOIN (
          SELECT
              cashbook_id,
              SUM(amount) as total_expenses
          FROM transactions
          WHERE type = 'expense'
          GROUP BY cashbook_id
      ) expense ON c.id = expense.cashbook_id
      LEFT JOIN (
          SELECT
              cashbook_id,
              COUNT(*) as transaction_count
          FROM transactions
          GROUP BY cashbook_id
      ) trans ON c.id = trans.cashbook_id
    `);
    console.log('✅ View created');
    
    // Verify setup
    console.log('\n🔍 Verifying setup...');
    
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('cashbooks', 'transactions', 'categories', 'cashbook_collaborators')
      ORDER BY table_name
    `;
    
    console.log('📋 Tables created:');
    tables.forEach(table => {
      console.log(`   ✓ ${table.table_name}`);
    });
    
    const categoryCount = await sql`
      SELECT type, COUNT(*) as count 
      FROM categories 
      WHERE is_default = true 
      GROUP BY type 
      ORDER BY type
    `;
    
    console.log('📋 Default categories:');
    categoryCount.forEach(cat => {
      console.log(`   ✓ ${cat.type}: ${cat.count} categories`);
    });
    
    console.log('\n🎉 Cashbook Management System setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Create TypeScript interfaces');
    console.log('2. Implement CashbookContext provider');
    console.log('3. Build UI components');
    console.log('4. Create API routes');
    
  } catch (error) {
    console.error('❌ Error setting up cashbook database:', error.message);
    process.exit(1);
  }
}

setupCashbookSimple();
