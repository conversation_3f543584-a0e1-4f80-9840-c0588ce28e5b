const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

async function testAuth() {
  console.log('🔐 Testing Authentication...\n');
  
  try {
    console.log('Testing /api/test-auth...');
    const result = await makeRequest('/api/test-auth');
    console.log('Status:', result.status);
    console.log('Response:', JSON.stringify(result.data, null, 2));
    
    if (result.data.authenticated) {
      console.log('\n✅ Authentication is working!');
      console.log('User ID:', result.data.user.id);
      console.log('User Name:', result.data.user.name);
      console.log('User Email:', result.data.user.email);
    } else {
      console.log('\n❌ Authentication failed');
      console.log('Error:', result.data.error);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
}

testAuth();
