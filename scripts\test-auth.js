const http = require('http');

function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', reject);
    req.end();
  });
}

async function testAuth() {
  console.log('🔐 Testing Authentication and API Access...\n');
  
  try {
    // Test cashbooks API
    console.log('📋 Testing GET /api/cashbooks...');
    const cashbooksResult = await makeRequest('/api/cashbooks');
    console.log(`   Status: ${cashbooksResult.status}`);
    
    if (cashbooksResult.status === 200) {
      console.log('✅ Cashbooks API accessible');
      console.log(`   Found ${cashbooksResult.data.data?.length || 0} cashbooks`);
      
      if (cashbooksResult.data.data && cashbooksResult.data.data.length > 0) {
        const testCashbook = cashbooksResult.data.data.find(cb => cb.name === 'Test Cashbook');
        if (testCashbook) {
          console.log('✅ Test Cashbook found in API response');
          console.log(`   ID: ${testCashbook.id}`);
          console.log(`   Role: ${testCashbook.user_role}`);
          console.log(`   Currency: ${testCashbook.currency}`);
          
          // Test categories API
          console.log('\n📂 Testing GET /api/categories...');
          const categoriesResult = await makeRequest('/api/categories');
          console.log(`   Status: ${categoriesResult.status}`);
          
          if (categoriesResult.status === 200) {
            console.log('✅ Categories API accessible');
            const categories = categoriesResult.data.data || [];
            const incomeCategories = categories.filter(c => c.type === 'income');
            const expenseCategories = categories.filter(c => c.type === 'expense');
            console.log(`   Income categories: ${incomeCategories.length}`);
            console.log(`   Expense categories: ${expenseCategories.length}`);
          } else {
            console.log('❌ Categories API not accessible');
            console.log(`   Error: ${categoriesResult.data.error || 'Unknown error'}`);
          }
          
          // Test transactions API for the test cashbook
          console.log(`\n📝 Testing GET /api/cashbooks/${testCashbook.id}/transactions...`);
          const transactionsResult = await makeRequest(`/api/cashbooks/${testCashbook.id}/transactions`);
          console.log(`   Status: ${transactionsResult.status}`);
          
          if (transactionsResult.status === 200) {
            console.log('✅ Transactions API accessible');
            console.log(`   Found ${transactionsResult.data.data?.length || 0} transactions`);
          } else {
            console.log('❌ Transactions API not accessible');
            console.log(`   Error: ${transactionsResult.data.error || 'Unknown error'}`);
          }
        } else {
          console.log('⚠️  Test Cashbook not found in API response');
        }
      }
    } else if (cashbooksResult.status === 401) {
      console.log('❌ Authentication failed - user not logged in');
      console.log('   This explains why Cash In/Out buttons are not visible');
      console.log('   The user needs to be authenticated to see the buttons');
    } else {
      console.log('❌ Cashbooks API error');
      console.log(`   Error: ${cashbooksResult.data.error || 'Unknown error'}`);
    }
    
    console.log('\n🎯 Summary:');
    if (cashbooksResult.status === 401) {
      console.log('   - User is NOT authenticated');
      console.log('   - This is why Cash In/Out buttons are not visible');
      console.log('   - Need to implement proper authentication flow');
    } else if (cashbooksResult.status === 200) {
      console.log('   - User IS authenticated');
      console.log('   - APIs are accessible');
      console.log('   - Issue might be in the frontend component rendering');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAuth();
