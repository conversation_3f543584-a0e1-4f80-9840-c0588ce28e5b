const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testCashInOut() {
  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('🧪 Testing Cash In/Out functionality...\n');
    
    // Get the current user
    const userId = '6f32b7fd-3242-44da-a98e-99165cfcf1f7';
    
    // Create a test cashbook if it doesn't exist
    console.log('📋 Creating test cashbook...');
    
    const existingCashbook = await sql`
      SELECT id, name FROM cashbooks WHERE owner_id = ${userId} AND name = 'Test Cashbook'
    `;
    
    let cashbookId;
    if (existingCashbook.length === 0) {
      const newCashbook = await sql`
        INSERT INTO cashbooks (name, description, currency, owner_id)
        VALUES ('Test Cashbook', 'Testing Cash In/Out functionality', 'USD', ${userId})
        RETURNING id, name
      `;
      cashbookId = newCashbook[0].id;
      console.log('✅ Test cashbook created:', newCashbook[0].name);
    } else {
      cashbookId = existingCashbook[0].id;
      console.log('✅ Using existing test cashbook:', existingCashbook[0].name);
    }
    
    // Get available categories
    console.log('\n📂 Checking available categories...');
    const incomeCategories = await sql`
      SELECT id, name FROM categories WHERE type = 'income' LIMIT 3
    `;
    
    const expenseCategories = await sql`
      SELECT id, name FROM categories WHERE type = 'expense' LIMIT 3
    `;
    
    console.log('Income categories:', incomeCategories.map(c => c.name).join(', '));
    console.log('Expense categories:', expenseCategories.map(c => c.name).join(', '));
    
    if (incomeCategories.length === 0 || expenseCategories.length === 0) {
      console.log('❌ Not enough categories available for testing');
      return;
    }
    
    // Test Cash In (Income Transaction)
    console.log('\n💰 Testing Cash In (Income Transaction)...');
    
    const incomeTransaction = await sql`
      INSERT INTO transactions (cashbook_id, amount, type, category_id, description, date, created_by)
      VALUES (
        ${cashbookId},
        500.00,
        'income',
        ${incomeCategories[0].id},
        'Test income transaction from Cash In',
        ${new Date().toISOString().split('T')[0]},
        ${userId}
      )
      RETURNING id, amount, type, description
    `;
    
    console.log('✅ Cash In transaction created:', {
      id: incomeTransaction[0].id,
      amount: incomeTransaction[0].amount,
      type: incomeTransaction[0].type,
      description: incomeTransaction[0].description
    });
    
    // Test Cash Out (Expense Transaction)
    console.log('\n💸 Testing Cash Out (Expense Transaction)...');
    
    const expenseTransaction = await sql`
      INSERT INTO transactions (cashbook_id, amount, type, category_id, description, date, created_by)
      VALUES (
        ${cashbookId},
        150.00,
        'expense',
        ${expenseCategories[0].id},
        'Test expense transaction from Cash Out',
        ${new Date().toISOString().split('T')[0]},
        ${userId}
      )
      RETURNING id, amount, type, description
    `;
    
    console.log('✅ Cash Out transaction created:', {
      id: expenseTransaction[0].id,
      amount: expenseTransaction[0].amount,
      type: expenseTransaction[0].type,
      description: expenseTransaction[0].description
    });
    
    // Check financial summary
    console.log('\n📊 Checking financial summary...');
    
    const summary = await sql`
      SELECT 
        total_income,
        total_expenses,
        current_balance,
        transaction_count
      FROM financial_summary 
      WHERE cashbook_id = ${cashbookId}
    `;
    
    if (summary.length > 0) {
      console.log('✅ Financial summary updated:', {
        total_income: summary[0].total_income,
        total_expenses: summary[0].total_expenses,
        current_balance: summary[0].current_balance,
        transaction_count: summary[0].transaction_count
      });
    } else {
      console.log('⚠️  Financial summary not found - triggers may not be working');
    }
    
    // Get recent transactions
    console.log('\n📝 Recent transactions in test cashbook:');
    
    const recentTransactions = await sql`
      SELECT 
        t.id,
        t.amount,
        t.type,
        t.description,
        t.date,
        c.name as category_name
      FROM transactions t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE t.cashbook_id = ${cashbookId}
      ORDER BY t.created_at DESC
      LIMIT 5
    `;
    
    recentTransactions.forEach((tx, index) => {
      console.log(`${index + 1}. ${tx.type.toUpperCase()}: $${tx.amount} - ${tx.description} (${tx.category_name})`);
    });
    
    console.log('\n🎉 Cash In/Out functionality test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Test cashbook created/found');
    console.log('   ✅ Income categories available');
    console.log('   ✅ Expense categories available');
    console.log('   ✅ Cash In (income) transaction created');
    console.log('   ✅ Cash Out (expense) transaction created');
    console.log('   ✅ Financial summary updated automatically');
    console.log('   ✅ Transaction history working');
    
    console.log('\n🌐 You can now test the UI at: http://localhost:3003/apps/cashbook');
    console.log(`   Navigate to the test cashbook and try the Cash In/Cash Out buttons!`);
    
  } catch (error) {
    console.error('❌ Error testing Cash In/Out functionality:', error);
  }
}

testCashInOut();
