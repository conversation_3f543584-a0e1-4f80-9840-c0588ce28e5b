const fetch = require('node-fetch');

async function testCashbookAPI() {
  const baseURL = 'http://localhost:3002';
  
  console.log('🧪 Testing Cashbook API Endpoints...\n');
  
  try {
    // Test 1: GET /api/cashbooks (should return empty array since no cashbooks exist)
    console.log('📋 Testing GET /api/cashbooks...');
    const getCashbooksResponse = await fetch(`${baseURL}/api/cashbooks`);
    const getCashbooksData = await getCashbooksResponse.json();
    
    if (getCashbooksResponse.ok) {
      console.log('✅ GET /api/cashbooks - Success');
      console.log(`   - Status: ${getCashbooksResponse.status}`);
      console.log(`   - Cashbooks count: ${getCashbooksData.data?.length || 0}`);
    } else {
      console.log('❌ GET /api/cashbooks - Failed');
      console.log(`   - Status: ${getCashbooksResponse.status}`);
      console.log(`   - Error: ${getCashbooksData.error}`);
    }
    
    // Test 2: POST /api/cashbooks (create a new cashbook)
    console.log('\n📝 Testing POST /api/cashbooks...');
    const createCashbookData = {
      name: 'Test Cashbook',
      description: 'A test cashbook for API verification',
      currency: 'USD'
    };
    
    const createCashbookResponse = await fetch(`${baseURL}/api/cashbooks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(createCashbookData)
    });
    
    const createCashbookResult = await createCashbookResponse.json();
    
    if (createCashbookResponse.ok) {
      console.log('✅ POST /api/cashbooks - Success');
      console.log(`   - Status: ${createCashbookResponse.status}`);
      console.log(`   - Created cashbook: ${createCashbookResult.data?.name}`);
      console.log(`   - Cashbook ID: ${createCashbookResult.data?.id}`);
      
      // Test 3: GET /api/cashbooks again (should now return 1 cashbook)
      console.log('\n🔄 Testing GET /api/cashbooks again...');
      const getCashbooksResponse2 = await fetch(`${baseURL}/api/cashbooks`);
      const getCashbooksData2 = await getCashbooksResponse2.json();
      
      if (getCashbooksResponse2.ok) {
        console.log('✅ GET /api/cashbooks (after creation) - Success');
        console.log(`   - Cashbooks count: ${getCashbooksData2.data?.length || 0}`);
      } else {
        console.log('❌ GET /api/cashbooks (after creation) - Failed');
      }
      
    } else {
      console.log('❌ POST /api/cashbooks - Failed');
      console.log(`   - Status: ${createCashbookResponse.status}`);
      console.log(`   - Error: ${createCashbookResult.error}`);
      if (createCashbookResult.details) {
        console.log(`   - Details: ${JSON.stringify(createCashbookResult.details)}`);
      }
    }
    
    // Test 4: GET /api/categories
    console.log('\n📂 Testing GET /api/categories...');
    const getCategoriesResponse = await fetch(`${baseURL}/api/categories`);
    const getCategoriesData = await getCategoriesResponse.json();
    
    if (getCategoriesResponse.ok) {
      console.log('✅ GET /api/categories - Success');
      console.log(`   - Categories count: ${getCategoriesData.data?.length || 0}`);
    } else {
      console.log('❌ GET /api/categories - Failed');
      console.log(`   - Error: ${getCategoriesData.error}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
  
  console.log('\n🏁 API testing completed!');
}

testCashbookAPI();
