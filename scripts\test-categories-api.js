const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.end();
  });
}

async function testCategoriesAPI() {
  console.log('🔍 Testing Categories API...\n');
  
  try {
    console.log('Testing /api/categories...');
    const result = await makeRequest('/api/categories');
    console.log('Status:', result.status);
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ Categories API working!');
      console.log('Total categories:', result.data.data.length);
      
      const categories = result.data.data;
      const incomeCategories = categories.filter(c => c.type === 'income');
      const expenseCategories = categories.filter(c => c.type === 'expense');
      
      console.log('\n📊 Category Breakdown:');
      console.log('Income categories:', incomeCategories.length);
      incomeCategories.slice(0, 5).forEach(cat => {
        console.log(`   - ${cat.name} (${cat.is_default ? 'default' : 'custom'})`);
      });
      
      console.log('Expense categories:', expenseCategories.length);
      expenseCategories.slice(0, 5).forEach(cat => {
        console.log(`   - ${cat.name} (${cat.is_default ? 'default' : 'custom'})`);
      });
      
      if (incomeCategories.length > 0 && expenseCategories.length > 0) {
        console.log('\n✅ Both income and expense categories are available!');
        console.log('The Cash In/Out modals should now show categories in their dropdowns.');
      } else {
        console.log('\n❌ Missing categories:');
        if (incomeCategories.length === 0) console.log('   - No income categories found');
        if (expenseCategories.length === 0) console.log('   - No expense categories found');
      }
      
    } else {
      console.log('❌ Categories API failed');
      console.log('Response:', JSON.stringify(result.data, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
}

testCategoriesAPI();
