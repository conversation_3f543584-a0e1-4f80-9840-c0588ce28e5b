const http = require('http');

function testServer() {
  console.log('🧪 Testing if server is accessible...\n');
  
  const options = {
    hostname: 'localhost',
    port: 3002,
    path: '/',
    method: 'GET'
  };
  
  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers, null, 2)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (res.statusCode === 200) {
        console.log('✅ Server is accessible!');
        if (data.includes('<html')) {
          console.log('✅ HTML content received');
        }
        if (data.includes('error') || data.includes('Error')) {
          console.log('⚠️  Response contains error text');
        }
      } else {
        console.log(`❌ Server returned status: ${res.statusCode}`);
        console.log('Response:', data.substring(0, 500));
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ Connection error:', error.message);
  });
  
  req.setTimeout(5000, () => {
    console.error('❌ Request timed out');
    req.destroy();
  });
  
  req.end();
}

testServer();
