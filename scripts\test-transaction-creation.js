#!/usr/bin/env node

/**
 * Test script to verify transaction creation functionality
 */

const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function testTransactionCreation() {
  console.log('🧪 Testing Transaction Creation Functionality...\n');

  try {
    const sql = neon(process.env.DATABASE_URL);

    // Test 1: Check if transactions table exists
    console.log('📋 Test 1: Database Schema Verification');
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'transactions'
      );
    `;
    
    if (tableExists[0].exists) {
      console.log('✅ Transactions table exists');
      
      // Check table structure
      const columns = await sql`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'transactions'
        ORDER BY ordinal_position;
      `;
      
      console.log('   Table columns:');
      columns.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(required)' : '(optional)'}`);
      });
    } else {
      console.log('❌ Transactions table does not exist');
      return;
    }

    // Test 2: Check if cashbooks table exists and has data
    console.log('\n💼 Test 2: Cashbooks Verification');
    const cashbooksCount = await sql`SELECT COUNT(*) as count FROM cashbooks`;
    console.log(`   Cashbooks in database: ${cashbooksCount[0].count}`);
    
    if (cashbooksCount[0].count === 0) {
      console.log('⚠️  No cashbooks found. Creating a test cashbook...');
      
      // Create a test cashbook
      const testCashbook = await sql`
        INSERT INTO cashbooks (name, currency, description, created_by)
        VALUES ('Test Cashbook', 'USD', 'Test cashbook for transaction testing', '6f32b7fd-3242-44da-a98e-99165cfcf1f7')
        RETURNING id, name
      `;
      
      console.log(`✅ Created test cashbook: ${testCashbook[0].name} (ID: ${testCashbook[0].id})`);
    }

    // Test 3: Check categories
    console.log('\n📂 Test 3: Categories Verification');
    const categoriesCount = await sql`SELECT COUNT(*) as count FROM categories`;
    const incomeCount = await sql`SELECT COUNT(*) as count FROM categories WHERE type = 'income'`;
    const expenseCount = await sql`SELECT COUNT(*) as count FROM categories WHERE type = 'expense'`;
    
    console.log(`   Total categories: ${categoriesCount[0].count}`);
    console.log(`   Income categories: ${incomeCount[0].count}`);
    console.log(`   Expense categories: ${expenseCount[0].count}`);
    
    if (categoriesCount[0].count === 0) {
      console.log('❌ No categories found. Please run the cashbook setup script first.');
      return;
    }

    // Test 4: Test API endpoint (if server is running)
    console.log('\n🌐 Test 4: API Endpoint Testing');
    try {
      const response = await fetch('http://localhost:3000/api/categories');
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Categories API is accessible');
        console.log(`   API returned ${result.data?.length || 0} categories`);
      } else {
        console.log('❌ Categories API returned error:', response.status);
      }
    } catch (error) {
      console.log('⚠️  Could not test API (server may not be running)');
    }

    // Test 5: Verify transaction creation logic
    console.log('\n🔧 Test 5: Transaction Creation Logic');
    
    // Get a sample cashbook and category for testing
    const sampleCashbook = await sql`SELECT id FROM cashbooks LIMIT 1`;
    const sampleIncomeCategory = await sql`SELECT id, name FROM categories WHERE type = 'income' LIMIT 1`;
    const sampleExpenseCategory = await sql`SELECT id, name FROM categories WHERE type = 'expense' LIMIT 1`;
    
    if (sampleCashbook.length > 0 && sampleIncomeCategory.length > 0) {
      console.log(`✅ Sample data available for testing:`);
      console.log(`   Cashbook ID: ${sampleCashbook[0].id}`);
      console.log(`   Income Category: ${sampleIncomeCategory[0].name} (${sampleIncomeCategory[0].id})`);
      console.log(`   Expense Category: ${sampleExpenseCategory[0].name} (${sampleExpenseCategory[0].id})`);
      
      // Test transaction insertion
      const testTransaction = await sql`
        INSERT INTO transactions (
          cashbook_id,
          amount,
          type,
          category_id,
          description,
          date,
          payment_method,
          created_by
        ) VALUES (
          ${sampleCashbook[0].id},
          100.50,
          'income',
          ${sampleIncomeCategory[0].id},
          'Test transaction for verification',
          ${new Date().toISOString().split('T')[0]},
          'cash',
          '6f32b7fd-3242-44da-a98e-99165cfcf1f7'
        )
        RETURNING id, amount, type, description
      `;
      
      console.log(`✅ Test transaction created successfully:`);
      console.log(`   ID: ${testTransaction[0].id}`);
      console.log(`   Amount: $${testTransaction[0].amount}`);
      console.log(`   Type: ${testTransaction[0].type}`);
      console.log(`   Description: ${testTransaction[0].description}`);
      
      // Clean up test transaction
      await sql`DELETE FROM transactions WHERE id = ${testTransaction[0].id}`;
      console.log('🧹 Test transaction cleaned up');
    }

    console.log('\n🎯 Summary:');
    console.log('✅ Database schema is properly set up');
    console.log('✅ Categories are available for transaction creation');
    console.log('✅ Transaction creation logic works correctly');
    console.log('✅ All components are ready for Cash In/Out functionality');
    
    console.log('\n📝 Next Steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Navigate to the cashbook app');
    console.log('3. Test Cash In/Out buttons and form submission');
    console.log('4. Verify transactions appear in the dashboard');
    console.log('5. Check that toast notifications are displayed');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testTransactionCreation();
