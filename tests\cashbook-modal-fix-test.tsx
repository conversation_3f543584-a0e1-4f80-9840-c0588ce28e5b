"use client"

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import CashInOutActions from '@/components/cashbook/CashInOutActions';

// Mock categories for testing
const mockCategories = [
  { id: '1', name: 'Sal<PERSON>', type: 'income' as const, is_default: true },
  { id: '2', name: 'Freelance', type: 'income' as const, is_default: false },
  { id: '3', name: 'Groceries', type: 'expense' as const, is_default: true },
  { id: '4', name: 'Transport', type: 'expense' as const, is_default: false },
];

// Mock transaction creation function
const mockOnCreateTransaction = async (data: any) => {
  console.log('Mock transaction created:', data);
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
};

export default function CashbookModalFixTest() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h1 className="text-2xl font-bold mb-2">Cash In/Out Modal Fix Test</h1>
          <p className="text-gray-600">
            This test verifies the fixes for mobile modal overlap and duplicate button removal.
          </p>
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-xl font-semibold mb-4 text-blue-800">Test Instructions</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-blue-700">Mobile Test (< 768px):</h3>
              <ul className="text-sm space-y-1 text-blue-600 ml-4">
                <li>• Resize browser to mobile width (< 768px)</li>
                <li>• Verify buttons appear at bottom of screen</li>
                <li>• Click Cash In or Cash Out button</li>
                <li>• Verify buttons disappear when modal opens</li>
                <li>• Verify buttons reappear when modal closes</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-blue-700">Desktop Test (≥ 768px):</h3>
              <ul className="text-sm space-y-1 text-blue-600 ml-4">
                <li>• Resize browser to desktop width (≥ 768px)</li>
                <li>• Verify buttons appear inline in this section</li>
                <li>• Click Cash In or Cash Out button</li>
                <li>• Verify buttons remain visible when modal opens</li>
                <li>• Verify modal doesn't overlap with buttons</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Component Test */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Interactive Test</h2>
          <p className="text-gray-600 mb-4">
            Click the buttons below to test the modal behavior on your current screen size:
          </p>
          <CashInOutActions
            cashbookId="test-cashbook-id"
            currency="USD"
            categories={mockCategories}
            onCreateTransaction={mockOnCreateTransaction}
            disabled={false}
          />
        </div>

        {/* Test Results */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="space-y-2">
            <Button 
              onClick={() => addTestResult("Mobile modal test - buttons hidden when modal open")}
              className="mr-2 mb-2"
            >
              ✓ Mobile Modal Test Passed
            </Button>
            <Button 
              onClick={() => addTestResult("Desktop modal test - buttons remain visible")}
              className="mr-2 mb-2"
            >
              ✓ Desktop Modal Test Passed
            </Button>
            <Button 
              onClick={() => addTestResult("Empty state test - no duplicate buttons found")}
              className="mr-2 mb-2"
            >
              ✓ Empty State Test Passed
            </Button>
          </div>
          
          {testResults.length > 0 && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
              <h3 className="font-semibold text-green-800 mb-2">Test Log:</h3>
              <ul className="text-sm text-green-700 space-y-1">
                {testResults.map((result, index) => (
                  <li key={index}>• {result}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Expected Behavior Summary */}
        <div className="bg-gray-100 p-6 rounded-lg border">
          <h2 className="text-xl font-semibold mb-4">Expected Behavior Summary</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">✅ Fixed Issues:</h3>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Mobile buttons hide when modal opens</li>
                <li>• No modal overlap on mobile devices</li>
                <li>• Removed duplicate buttons from empty states</li>
                <li>• Clean interface without visual clutter</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">✅ Preserved Functionality:</h3>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Desktop buttons always visible</li>
                <li>• All transaction creation works</li>
                <li>• Responsive design maintained</li>
                <li>• Header buttons remain primary interface</li>
              </ul>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}
