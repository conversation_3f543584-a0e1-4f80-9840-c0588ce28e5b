#!/usr/bin/env node

/**
 * Test script to verify Cashbook Select component fixes
 * This script checks that all Select components in the cashbook system
 * have proper non-empty values and won't cause the Radix UI Select error
 */

const fs = require('fs');
const path = require('path');

async function testCashbookSelectFixes() {
  console.log('🧪 Testing Cashbook Select Component Fixes\n');
  console.log('=' .repeat(60));
  
  let allTestsPassed = true;
  
  function runTest(testName, testFunction) {
    console.log(`\n📋 ${testName}`);
    console.log('-' .repeat(40));
    try {
      const result = testFunction();
      if (result) {
        console.log('✅ PASSED');
      } else {
        console.log('❌ FAILED');
        allTestsPassed = false;
      }
      return result;
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      allTestsPassed = false;
      return false;
    }
  }
  
  // Test 1: Check CashbookListScreen component fixes
  runTest('CashbookListScreen - Filter SelectItems', () => {
    const filePath = path.join('components', 'cashbook', 'CashbookListScreen.tsx');
    if (!fs.existsSync(filePath)) {
      console.log('   ⚠️  File not found');
      return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (content.includes('value="all-currencies"') && 
        content.includes('value="all-roles"') &&
        !content.includes('<SelectItem value="">')) {
      console.log('   ✅ All empty string values replaced');
      console.log('   ✅ Currency filter: "all-currencies"');
      console.log('   ✅ Role filter: "all-roles"');
      return true;
    } else {
      console.log('   ❌ Still contains empty string values in filter SelectItems');
      return false;
    }
  });
  
  // Test 2: Check TransactionList component fixes
  runTest('TransactionList - Category Filter SelectItem', () => {
    const filePath = path.join('components', 'cashbook', 'TransactionList.tsx');
    if (!fs.existsSync(filePath)) {
      console.log('   ⚠️  File not found');
      return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (content.includes('value="all-categories"') && 
        !content.includes('<SelectItem value="">All Categories</SelectItem>')) {
      console.log('   ✅ Empty string value replaced with "all-categories"');
      return true;
    } else {
      console.log('   ❌ Still contains empty string values');
      return false;
    }
  });
  
  // Test 3: Check CashbookDetailScreen component fixes
  runTest('CashbookDetailScreen - Category Filter SelectItem', () => {
    const filePath = path.join('components', 'cashbook', 'CashbookDetailScreen.tsx');
    if (!fs.existsSync(filePath)) {
      console.log('   ⚠️  File not found');
      return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (content.includes('value="all-categories"') && 
        !content.includes('<SelectItem value="">All Categories</SelectItem>')) {
      console.log('   ✅ Empty string value replaced with "all-categories"');
      return true;
    } else {
      console.log('   ❌ Still contains empty string values');
      return false;
    }
  });
  
  // Test 4: Check state initialization
  runTest('State Initialization - Non-empty Default Values', () => {
    const filesToCheck = [
      'components/cashbook/CashbookListScreen.tsx',
      'components/cashbook/TransactionList.tsx',
      'components/cashbook/CashbookDetailScreen.tsx'
    ];

    let allInitialized = true;

    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');

        // Check for specific problematic state initializations
        if (content.includes("selectedCurrency, setSelectedCurrency] = useState('')") ||
            content.includes("selectedRole, setSelectedRole] = useState('')") ||
            content.includes("selectedCategory, setSelectedCategory] = useState('')")) {
          console.log(`   ❌ ${path.basename(file)} still has empty string filter state initialization`);
          allInitialized = false;
        }
      }
    }

    if (allInitialized) {
      console.log('   ✅ All filter states properly initialized with non-empty values');
      return true;
    } else {
      return false;
    }
  });
  
  // Test 5: Check filtering logic
  runTest('Filtering Logic - Proper Non-empty Value Checks', () => {
    const filesToCheck = [
      'components/cashbook/CashbookListScreen.tsx',
      'components/cashbook/TransactionList.tsx',
      'components/cashbook/CashbookDetailScreen.tsx'
    ];
    
    let allLogicUpdated = true;
    
    for (const file of filesToCheck) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for updated filtering logic
        if (content.includes("=== 'all-currencies'") || 
            content.includes("=== 'all-roles'") || 
            content.includes("=== 'all-categories'") ||
            content.includes("!== 'all-categories'")) {
          console.log(`   ✅ ${path.basename(file)} has updated filtering logic`);
        } else if (content.includes('selectedCurrency') || content.includes('selectedRole') || content.includes('selectedCategory')) {
          console.log(`   ❌ ${path.basename(file)} may have outdated filtering logic`);
          allLogicUpdated = false;
        }
      }
    }
    
    if (allLogicUpdated) {
      console.log('   ✅ All filtering logic properly updated');
      return true;
    } else {
      return false;
    }
  });
  
  // Test 6: Global check for any remaining empty SelectItem values
  runTest('Global Check - No Remaining Empty SelectItem Values', () => {
    const cashbookDir = path.join('components', 'cashbook');
    if (!fs.existsSync(cashbookDir)) {
      console.log('   ⚠️  Cashbook directory not found');
      return false;
    }
    
    const files = fs.readdirSync(cashbookDir).filter(file => file.endsWith('.tsx'));
    let foundEmptyValues = false;
    const emptyValuePattern = /<SelectItem\s+value=""\s*[^>]*>/g;
    
    for (const file of files) {
      const filePath = path.join(cashbookDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const matches = content.match(emptyValuePattern);
      
      if (matches) {
        console.log(`   ❌ Found empty SelectItem values in ${file}:`);
        matches.forEach(match => console.log(`      ${match}`));
        foundEmptyValues = true;
      }
    }
    
    if (!foundEmptyValues) {
      console.log('   ✅ No empty SelectItem values found in any cashbook component');
      return true;
    } else {
      return false;
    }
  });
  
  // Final Summary
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 CASHBOOK SELECT COMPONENT FIXES SUMMARY');
  console.log('=' .repeat(60));
  
  if (allTestsPassed) {
    console.log('\n🎉 ALL CASHBOOK SELECT COMPONENT FIXES SUCCESSFUL! 🎉');
    console.log('\n✨ Cashbook Select Component Error Resolution:');
    console.log('   ✅ CashbookListScreen: Currency and role filter values fixed');
    console.log('   ✅ TransactionList: Category filter value fixed');
    console.log('   ✅ CashbookDetailScreen: Category filter value fixed');
    console.log('   ✅ State initialization updated to use non-empty defaults');
    console.log('   ✅ Filtering logic updated to handle new values');
    console.log('   ✅ No remaining empty string SelectItem values');
    console.log('\n🚀 The Cashbook application should now load without Select component errors!');
  } else {
    console.log('\n❌ SOME TESTS FAILED');
    console.log('\n🔧 Please review the failed tests and fix any remaining issues.');
  }
  
  console.log('\n' + '=' .repeat(60));
  return allTestsPassed;
}

// Run the tests
testCashbookSelectFixes().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
