import { test, expect } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
  name: 'Test User',
};

const TEST_CASHBOOK = {
  name: 'Test Cashbook',
  description: 'A test cashbook for E2E testing',
  currency: 'USD',
};

const TEST_TRANSACTION = {
  amount: '25.50',
  type: 'expense',
  category: 'Food & Dining',
  description: 'Coffee and pastry',
};

test.describe('Cashbook Management System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Mock authentication if needed
    // await page.evaluate(() => {
    //   localStorage.setItem('auth-token', 'mock-token');
    // });
  });

  test.describe('Navigation and Layout', () => {
    test('should display main navigation', async ({ page }) => {
      await expect(page.locator('nav')).toBeVisible();
      await expect(page.getByText('Apps')).toBeVisible();
      await expect(page.getByText('Calculator')).toBeVisible();
    });

    test('should navigate to cashbook app', async ({ page }) => {
      await page.click('text=Apps');
      await page.click('text=Cashbook');
      await expect(page).toHaveURL('/apps/cashbook');
      await expect(page.getByText('Cashbook Management')).toBeVisible();
    });

    test('should be responsive on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/apps/cashbook');
      
      // Check mobile navigation
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
      
      // Check responsive layout
      await expect(page.locator('.grid')).toHaveClass(/grid-cols-1/);
    });
  });

  test.describe('Cashbook Management', () => {
    test('should create a new cashbook', async ({ page }) => {
      await page.goto('/apps/cashbook');
      
      // Click create cashbook button
      await page.click('text=Create Cashbook');
      
      // Fill out the form
      await page.fill('[data-testid="cashbook-name"]', TEST_CASHBOOK.name);
      await page.fill('[data-testid="cashbook-description"]', TEST_CASHBOOK.description);
      await page.selectOption('[data-testid="cashbook-currency"]', TEST_CASHBOOK.currency);
      
      // Submit the form
      await page.click('text=Create Cashbook');
      
      // Verify cashbook was created
      await expect(page.getByText(TEST_CASHBOOK.name)).toBeVisible();
      await expect(page.getByText('Cashbook created successfully')).toBeVisible();
    });

    test('should display cashbook list', async ({ page }) => {
      await page.goto('/apps/cashbook');
      
      // Wait for cashbooks to load
      await page.waitForSelector('[data-testid="cashbook-list"]');
      
      // Check if cashbooks are displayed
      await expect(page.locator('[data-testid="cashbook-card"]')).toHaveCount({ min: 0 });
    });

    test('should navigate to cashbook detail', async ({ page }) => {
      await page.goto('/apps/cashbook');
      
      // Wait for cashbooks to load
      await page.waitForSelector('[data-testid="cashbook-card"]');
      
      // Click on first cashbook
      await page.click('[data-testid="cashbook-card"]:first-child');
      
      // Verify navigation to detail page
      await expect(page).toHaveURL(/\/apps\/cashbook\/[^\/]+$/);
      await expect(page.getByText('Financial Overview')).toBeVisible();
    });

    test('should edit cashbook details', async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('[data-testid="cashbook-card"]:first-child');
      
      // Click edit button
      await page.click('[data-testid="edit-cashbook"]');
      
      // Update cashbook name
      await page.fill('[data-testid="cashbook-name"]', 'Updated Cashbook Name');
      
      // Save changes
      await page.click('text=Save Changes');
      
      // Verify update
      await expect(page.getByText('Updated Cashbook Name')).toBeVisible();
    });
  });

  test.describe('Transaction Management', () => {
    test.beforeEach(async ({ page }) => {
      // Navigate to a cashbook detail page
      await page.goto('/apps/cashbook');
      await page.waitForSelector('[data-testid="cashbook-card"]');
      await page.click('[data-testid="cashbook-card"]:first-child');
    });

    test('should create a new transaction', async ({ page }) => {
      // Click add transaction button
      await page.click('text=Add Transaction');
      
      // Fill out transaction form
      await page.fill('[data-testid="transaction-amount"]', TEST_TRANSACTION.amount);
      await page.click(`[data-testid="transaction-type-${TEST_TRANSACTION.type}"]`);
      await page.selectOption('[data-testid="transaction-category"]', TEST_TRANSACTION.category);
      await page.fill('[data-testid="transaction-description"]', TEST_TRANSACTION.description);
      
      // Submit transaction
      await page.click('text=Create Transaction');
      
      // Verify transaction was created
      await expect(page.getByText(TEST_TRANSACTION.description)).toBeVisible();
      await expect(page.getByText(`$${TEST_TRANSACTION.amount}`)).toBeVisible();
    });

    test('should use quick actions', async ({ page }) => {
      // Click on a quick action (e.g., Coffee)
      await page.click('[data-testid="quick-action-coffee"]');
      
      // Verify quick action modal opens
      await expect(page.getByText('Quick Coffee')).toBeVisible();
      
      // Modify amount if needed
      await page.fill('[data-testid="quick-amount"]', '5.00');
      
      // Submit quick transaction
      await page.click('text=Add Transaction');
      
      // Verify transaction appears in list
      await expect(page.getByText('Coffee')).toBeVisible();
      await expect(page.getByText('$5.00')).toBeVisible();
    });

    test('should filter transactions', async ({ page }) => {
      // Wait for transactions to load
      await page.waitForSelector('[data-testid="transaction-list"]');
      
      // Apply type filter
      await page.selectOption('[data-testid="filter-type"]', 'expense');
      
      // Apply search filter
      await page.fill('[data-testid="search-transactions"]', 'coffee');
      
      // Verify filtered results
      await expect(page.locator('[data-testid="transaction-item"]')).toHaveCount({ min: 0 });
    });

    test('should edit transaction', async ({ page }) => {
      // Wait for transactions to load
      await page.waitForSelector('[data-testid="transaction-item"]');
      
      // Click edit on first transaction
      await page.click('[data-testid="transaction-item"]:first-child [data-testid="edit-transaction"]');
      
      // Update transaction amount
      await page.fill('[data-testid="transaction-amount"]', '30.00');
      
      // Save changes
      await page.click('text=Update Transaction');
      
      // Verify update
      await expect(page.getByText('$30.00')).toBeVisible();
    });

    test('should delete transaction', async ({ page }) => {
      // Wait for transactions to load
      await page.waitForSelector('[data-testid="transaction-item"]');
      
      // Get initial transaction count
      const initialCount = await page.locator('[data-testid="transaction-item"]').count();
      
      // Click delete on first transaction
      await page.click('[data-testid="transaction-item"]:first-child [data-testid="delete-transaction"]');
      
      // Confirm deletion
      await page.click('text=Delete');
      
      // Verify transaction was deleted
      await expect(page.locator('[data-testid="transaction-item"]')).toHaveCount(initialCount - 1);
    });
  });

  test.describe('Category Management', () => {
    test('should create custom category', async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('[data-testid="cashbook-card"]:first-child');
      
      // Navigate to add transaction
      await page.click('text=Add Transaction');
      
      // Try to create new category
      await page.click('[data-testid="create-category"]');
      
      // Fill category form
      await page.fill('[data-testid="category-name"]', 'Custom Category');
      await page.selectOption('[data-testid="category-type"]', 'expense');
      
      // Create category
      await page.click('text=Create Category');
      
      // Verify category appears in selector
      await expect(page.getByText('Custom Category')).toBeVisible();
    });
  });

  test.describe('Collaboration Features', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('[data-testid="cashbook-card"]:first-child');
    });

    test('should manage collaborators', async ({ page }) => {
      // Click manage collaborators
      await page.click('[data-testid="manage-collaborators"]');
      
      // Verify collaborator management page
      await expect(page.getByText('Manage Collaborators')).toBeVisible();
      await expect(page.getByText('Invite Collaborator')).toBeVisible();
    });

    test('should invite collaborator', async ({ page }) => {
      await page.click('[data-testid="manage-collaborators"]');
      
      // Click invite button
      await page.click('text=Invite Collaborator');
      
      // Fill invitation form
      await page.fill('[data-testid="invite-email"]', '<EMAIL>');
      await page.selectOption('[data-testid="invite-role"]', 'editor');
      
      // Send invitation
      await page.click('text=Send Invitation');
      
      // Verify invitation sent
      await expect(page.getByText('Invitation sent successfully')).toBeVisible();
    });
  });

  test.describe('Financial Overview', () => {
    test('should display financial metrics', async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('[data-testid="cashbook-card"]:first-child');
      
      // Verify financial overview components
      await expect(page.getByText('Total Income')).toBeVisible();
      await expect(page.getByText('Total Expenses')).toBeVisible();
      await expect(page.getByText('Current Balance')).toBeVisible();
      await expect(page.getByText('Transactions')).toBeVisible();
    });

    test('should update metrics after transaction', async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('[data-testid="cashbook-card"]:first-child');
      
      // Get initial balance
      const initialBalance = await page.textContent('[data-testid="current-balance"]');
      
      // Add income transaction
      await page.click('text=Add Transaction');
      await page.fill('[data-testid="transaction-amount"]', '100.00');
      await page.click('[data-testid="transaction-type-income"]');
      await page.selectOption('[data-testid="transaction-category"]', 'Salary');
      await page.click('text=Create Transaction');
      
      // Verify balance updated
      await expect(page.locator('[data-testid="current-balance"]')).not.toHaveText(initialBalance || '');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      await page.goto('/apps/cashbook');
      
      // Verify error message is displayed
      await expect(page.getByText('Unable to connect')).toBeVisible();
      await expect(page.getByText('Try Again')).toBeVisible();
    });

    test('should validate form inputs', async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('text=Create Cashbook');
      
      // Try to submit empty form
      await page.click('text=Create Cashbook');
      
      // Verify validation errors
      await expect(page.getByText('Name is required')).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await page.goto('/apps/cashbook');
      
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter');
      
      // Verify focus management
      await expect(page.locator(':focus')).toBeVisible();
    });

    test('should have proper ARIA labels', async ({ page }) => {
      await page.goto('/apps/cashbook');
      
      // Check for ARIA labels
      await expect(page.locator('[aria-label]')).toHaveCount({ min: 1 });
      await expect(page.locator('[role="button"]')).toHaveCount({ min: 1 });
    });
  });

  test.describe('Performance', () => {
    test('should load quickly', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/apps/cashbook');
      await page.waitForSelector('[data-testid="cashbook-list"]');
      const loadTime = Date.now() - startTime;
      
      // Verify page loads within reasonable time
      expect(loadTime).toBeLessThan(3000);
    });

    test('should handle large datasets', async ({ page }) => {
      await page.goto('/apps/cashbook');
      await page.click('[data-testid="cashbook-card"]:first-child');
      
      // Verify pagination or virtualization for large lists
      await expect(page.locator('[data-testid="transaction-item"]')).toHaveCount({ max: 50 });
    });
  });
});
