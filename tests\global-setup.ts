import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Setup test database
    console.log('📊 Setting up test database...');
    
    // You can add database seeding here
    // await seedTestDatabase();
    
    // Setup test user authentication
    console.log('👤 Setting up test authentication...');
    
    // Navigate to login page and authenticate
    await page.goto('http://localhost:3000/auth/signin');
    
    // Mock authentication or perform actual login
    await page.evaluate(() => {
      // Mock authentication token
      localStorage.setItem('auth-token', 'test-auth-token');
      localStorage.setItem('user-id', 'test-user-id');
    });

    // Create test data
    console.log('📝 Creating test data...');
    
    // You can create test cashbooks, transactions, etc. here
    // await createTestCashbooks();
    // await createTestTransactions();

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

// Helper function to seed test database
async function seedTestDatabase() {
  // Add your database seeding logic here
  // This could involve:
  // - Creating test users
  // - Creating default categories
  // - Setting up test cashbooks
  // - Creating sample transactions
  
  console.log('Seeding test database with default data...');
  
  // Example: Create default categories
  const defaultCategories = [
    { name: 'Food & Dining', type: 'expense', is_default: true },
    { name: 'Transportation', type: 'expense', is_default: true },
    { name: 'Shopping', type: 'expense', is_default: true },
    { name: 'Entertainment', type: 'expense', is_default: true },
    { name: 'Healthcare', type: 'expense', is_default: true },
    { name: 'Salary', type: 'income', is_default: true },
    { name: 'Freelance', type: 'income', is_default: true },
    { name: 'Investment', type: 'income', is_default: true },
  ];
  
  // Insert categories into database
  // await insertCategories(defaultCategories);
}

// Helper function to create test cashbooks
async function createTestCashbooks() {
  const testCashbooks = [
    {
      name: 'Personal Finance',
      description: 'My personal cashbook for daily expenses',
      currency: 'USD',
    },
    {
      name: 'Business Expenses',
      description: 'Business-related income and expenses',
      currency: 'USD',
    },
    {
      name: 'Vacation Fund',
      description: 'Saving for vacation',
      currency: 'EUR',
    },
  ];
  
  // Create cashbooks via API or database
  // await insertCashbooks(testCashbooks);
}

// Helper function to create test transactions
async function createTestTransactions() {
  const testTransactions = [
    {
      amount: 25.50,
      type: 'expense',
      description: 'Coffee and pastry',
      category: 'Food & Dining',
      date: new Date().toISOString().split('T')[0],
    },
    {
      amount: 3000.00,
      type: 'income',
      description: 'Monthly salary',
      category: 'Salary',
      date: new Date().toISOString().split('T')[0],
    },
    {
      amount: 50.00,
      type: 'expense',
      description: 'Gas for car',
      category: 'Transportation',
      date: new Date().toISOString().split('T')[0],
    },
  ];
  
  // Create transactions via API or database
  // await insertTransactions(testTransactions);
}

export default globalSetup;
