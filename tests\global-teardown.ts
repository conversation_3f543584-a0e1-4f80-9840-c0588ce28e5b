import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  try {
    // Clean up test database
    console.log('🗑️ Cleaning up test database...');
    await cleanupTestDatabase();

    // Clean up test files
    console.log('📁 Cleaning up test files...');
    await cleanupTestFiles();

    // Clean up test authentication
    console.log('🔐 Cleaning up test authentication...');
    await cleanupTestAuth();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid failing the test run
  }
}

// Helper function to clean up test database
async function cleanupTestDatabase() {
  // Add your database cleanup logic here
  // This could involve:
  // - Deleting test transactions
  // - Deleting test cashbooks
  // - Deleting test users
  // - Resetting database state
  
  console.log('Removing test data from database...');
  
  // Example cleanup queries:
  // await deleteTestTransactions();
  // await deleteTestCashbooks();
  // await deleteTestUsers();
  // await deleteTestCategories();
}

// Helper function to clean up test files
async function cleanupTestFiles() {
  // Clean up any files created during testing
  // This could involve:
  // - Removing uploaded test files
  // - Cleaning temporary directories
  // - Removing test exports
  
  console.log('Removing test files...');
  
  // Example file cleanup:
  // await removeTestUploads();
  // await cleanTempDirectories();
}

// Helper function to clean up test authentication
async function cleanupTestAuth() {
  // Clean up authentication-related test data
  // This could involve:
  // - Revoking test tokens
  // - Cleaning session data
  // - Removing test API keys
  
  console.log('Cleaning up test authentication data...');
  
  // Example auth cleanup:
  // await revokeTestTokens();
  // await cleanTestSessions();
}

// Specific cleanup functions
async function deleteTestTransactions() {
  // Delete all transactions created during testing
  // You might want to identify test transactions by:
  // - Created by test user
  // - Created during test timeframe
  // - Marked with test flag
  
  console.log('Deleting test transactions...');
  
  // Example SQL:
  // DELETE FROM transactions WHERE created_by = 'test-user-id';
  // DELETE FROM transactions WHERE description LIKE '%TEST%';
}

async function deleteTestCashbooks() {
  // Delete all cashbooks created during testing
  
  console.log('Deleting test cashbooks...');
  
  // Example SQL:
  // DELETE FROM cashbooks WHERE owner_id = 'test-user-id';
  // DELETE FROM cashbooks WHERE name LIKE '%Test%';
}

async function deleteTestUsers() {
  // Delete test users (be careful with this in shared environments)
  
  console.log('Deleting test users...');
  
  // Example SQL:
  // DELETE FROM users WHERE email LIKE '%<EMAIL>';
  // DELETE FROM users WHERE created_at > 'test-start-time';
}

async function deleteTestCategories() {
  // Delete custom categories created during testing
  // (Don't delete default categories)
  
  console.log('Deleting test categories...');
  
  // Example SQL:
  // DELETE FROM categories WHERE is_default = false AND created_by = 'test-user-id';
}

async function removeTestUploads() {
  // Remove any files uploaded during testing
  
  console.log('Removing test uploads...');
  
  // Example file operations:
  // await fs.rmdir('./uploads/test', { recursive: true });
  // await fs.rmdir('./temp/test-*', { recursive: true });
}

async function cleanTempDirectories() {
  // Clean up temporary directories created during testing
  
  console.log('Cleaning temporary directories...');
  
  // Example cleanup:
  // await fs.rmdir('./tmp/test-*', { recursive: true });
  // await fs.rmdir('./cache/test-*', { recursive: true });
}

async function revokeTestTokens() {
  // Revoke any authentication tokens created during testing
  
  console.log('Revoking test tokens...');
  
  // Example token cleanup:
  // await revokeTokensForUser('test-user-id');
  // await cleanExpiredTokens();
}

async function cleanTestSessions() {
  // Clean up session data from testing
  
  console.log('Cleaning test sessions...');
  
  // Example session cleanup:
  // await deleteSessionsForUser('test-user-id');
  // await cleanExpiredSessions();
}

export default globalTeardown;
