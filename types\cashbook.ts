// Cashbook Management System TypeScript Interfaces and Types

// Base entity interface
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at?: string;
}

// User interface (extends existing user type)
export interface CashbookUser {
  id: string;
  email: string;
  full_name: string;
  role: string;
  created_at: string;
}

// Cashbook interface
export interface Cashbook extends BaseEntity {
  name: string;
  description?: string;
  owner_id: string;
  currency: string;
  // Computed fields from view
  user_role?: 'owner' | 'editor' | 'viewer';
  total_income?: number;
  total_expenses?: number;
  current_balance?: number;
  transaction_count?: number;
  // Related data
  owner?: CashbookUser;
  collaborators?: CashbookCollaborator[];
}

// Transaction interface
export interface Transaction extends BaseEntity {
  cashbook_id: string;
  amount: number;
  type: 'income' | 'expense';
  category_id: string;
  description?: string;
  date: string;
  created_by: string;
  // Related data
  category?: Category;
  created_by_user?: CashbookUser;
  cashbook?: Cashbook;
}

// Category interface
export interface Category extends BaseEntity {
  name: string;
  type: 'income' | 'expense';
  is_default: boolean;
  created_by?: string;
  // Related data
  created_by_user?: CashbookUser;
}

// Cashbook collaborator interface
export interface CashbookCollaborator extends BaseEntity {
  cashbook_id: string;
  user_id: string;
  role: 'owner' | 'editor' | 'viewer';
  // Related data
  user?: CashbookUser;
  cashbook?: Cashbook;
}

// Form data interfaces for creating/updating entities
export interface CreateCashbookData {
  name: string;
  description?: string;
  currency: string;
}

export interface UpdateCashbookData {
  name?: string;
  description?: string;
  currency?: string;
}

export interface CreateTransactionData {
  cashbook_id: string;
  amount: number;
  type: 'income' | 'expense';
  category_id: string;
  description?: string;
  date: string;
}

export interface UpdateTransactionData {
  amount?: number;
  type?: 'income' | 'expense';
  category_id?: string;
  description?: string;
  date?: string;
}

export interface CreateCategoryData {
  name: string;
  type: 'income' | 'expense';
}

export interface InviteCollaboratorData {
  cashbook_id: string;
  email: string;
  role: 'editor' | 'viewer';
}

export interface UpdateCollaboratorData {
  role: 'editor' | 'viewer';
}

// Financial summary interface
export interface FinancialSummary {
  total_income: number;
  total_expenses: number;
  current_balance: number;
  transaction_count: number;
  currency: string;
}

// Filter and pagination interfaces
export interface TransactionFilters {
  type?: 'income' | 'expense';
  category_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CashbookFilters {
  search?: string;
  currency?: string;
  role?: 'owner' | 'editor' | 'viewer';
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Error handling types
export enum CashbookErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  DUPLICATE_ERROR = 'DUPLICATE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR'
}

export interface CashbookError {
  type: CashbookErrorType;
  message: string;
  field?: string;
  details?: any;
  code?: string;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: CashbookError;
  message?: string;
}

export interface ApiErrorResponse {
  success: false;
  error: CashbookError;
  message: string;
}

export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

// Permission types
export type CashbookPermission = 'view' | 'edit' | 'manage' | 'delete';

export interface PermissionCheck {
  cashbook_id: string;
  user_id: string;
  permission: CashbookPermission;
}

// Currency types
export interface Currency {
  code: string;
  name: string;
  symbol: string;
}

export const SUPPORTED_CURRENCIES: Currency[] = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'NPR', name: 'Nepalese Rupee', symbol: 'Rs.' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

// Validation schemas (using Zod-like structure for reference)
export interface ValidationSchema {
  cashbook: {
    name: { required: true; minLength: 1; maxLength: 100 };
    description: { maxLength: 500 };
    currency: { required: true; enum: string[] };
  };
  transaction: {
    amount: { required: true; min: 0.01; max: *********.99 };
    type: { required: true; enum: ['income', 'expense'] };
    category_id: { required: true; format: 'uuid' };
    description: { maxLength: 500 };
    date: { required: true; format: 'date' };
  };
  category: {
    name: { required: true; minLength: 1; maxLength: 50 };
    type: { required: true; enum: ['income', 'expense'] };
  };
  collaborator: {
    email: { required: true; format: 'email' };
    role: { required: true; enum: ['editor', 'viewer'] };
  };
}

// Loading and UI state types
export interface LoadingState {
  cashbooks: boolean;
  transactions: boolean;
  categories: boolean;
  collaborators: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

export interface UIState {
  selectedCashbook: string | null;
  selectedTransaction: string | null;
  showCreateModal: boolean;
  showEditModal: boolean;
  showDeleteModal: boolean;
  showCollaboratorModal: boolean;
  filters: TransactionFilters;
  pagination: PaginationParams;
}

// Context state interface
export interface CashbookContextState {
  // Data
  cashbooks: Cashbook[];
  currentCashbook: Cashbook | null;
  transactions: Transaction[];
  categories: Category[];
  collaborators: CashbookCollaborator[];
  
  // UI State
  loading: LoadingState;
  error: CashbookError | null;
  ui: UIState;
  
  // Financial summary
  financialSummary: FinancialSummary | null;
}

// Action types for context reducer
export enum CashbookActionType {
  // Loading actions
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  CLEAR_ERROR = 'CLEAR_ERROR',
  
  // Cashbook actions
  SET_CASHBOOKS = 'SET_CASHBOOKS',
  SET_CURRENT_CASHBOOK = 'SET_CURRENT_CASHBOOK',
  ADD_CASHBOOK = 'ADD_CASHBOOK',
  UPDATE_CASHBOOK = 'UPDATE_CASHBOOK',
  DELETE_CASHBOOK = 'DELETE_CASHBOOK',
  
  // Transaction actions
  SET_TRANSACTIONS = 'SET_TRANSACTIONS',
  ADD_TRANSACTION = 'ADD_TRANSACTION',
  UPDATE_TRANSACTION = 'UPDATE_TRANSACTION',
  DELETE_TRANSACTION = 'DELETE_TRANSACTION',
  
  // Category actions
  SET_CATEGORIES = 'SET_CATEGORIES',
  ADD_CATEGORY = 'ADD_CATEGORY',
  
  // Collaborator actions
  SET_COLLABORATORS = 'SET_COLLABORATORS',
  ADD_COLLABORATOR = 'ADD_COLLABORATOR',
  UPDATE_COLLABORATOR = 'UPDATE_COLLABORATOR',
  DELETE_COLLABORATOR = 'DELETE_COLLABORATOR',
  
  // UI actions
  SET_UI_STATE = 'SET_UI_STATE',
  SET_FILTERS = 'SET_FILTERS',
  SET_PAGINATION = 'SET_PAGINATION',
  
  // Financial summary
  SET_FINANCIAL_SUMMARY = 'SET_FINANCIAL_SUMMARY',
}

export interface CashbookAction {
  type: CashbookActionType;
  payload?: any;
}

// Utility types
export type CashbookRole = 'owner' | 'editor' | 'viewer';
export type TransactionType = 'income' | 'expense';
export type CategoryType = 'income' | 'expense';

// Export default currencies for easy access
export const DEFAULT_CURRENCY = 'USD';
export const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  NPR: 'Rs.',
  INR: '₹',
  CAD: 'C$',
  AUD: 'A$',
};
